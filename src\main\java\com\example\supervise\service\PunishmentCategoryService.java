package com.example.supervise.service;

import com.example.supervise.dto.PunishmentCategoryRequest;
import com.example.supervise.entity.PunishmentCategory;
import com.example.supervise.mapper.PunishmentCategoryMapper;
import com.example.supervise.util.Response;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 惩罚类型服务类
 * 处理惩罚类型相关的业务逻辑
 */
@Service
public class PunishmentCategoryService {

    private final PunishmentCategoryMapper punishmentCategoryMapper;

    /**
     * 构造函数，注入依赖
     */
    public PunishmentCategoryService(PunishmentCategoryMapper punishmentCategoryMapper) {
        this.punishmentCategoryMapper = punishmentCategoryMapper;
    }

    /**
     * 获取监督人的所有惩罚类型
     * @param supervisorId 监督人ID
     * @return 惩罚类型列表JSON字符串
     */
    public String getPunishmentCategories(Long supervisorId) {
        try {
            List<PunishmentCategory> categories = punishmentCategoryMapper.findBySupervisorId(supervisorId);
            return Response.toJSON(200, "获取惩罚类型列表成功", categories);
        } catch (Exception e) {
            return Response.toJSON(500, "获取惩罚类型列表失败：" + e.getMessage(), null);
        }
    }

    /**
     * 根据类型ID获取惩罚类型详情
     * @param categoryId 类型ID
     * @param currentUserId 当前用户ID
     * @return 惩罚类型详情JSON字符串
     */
    public String getPunishmentCategoryById(Long categoryId, Long currentUserId) {
        try {
            PunishmentCategory category = punishmentCategoryMapper.findByCategoryId(categoryId);
            if (category == null) {
                return Response.toJSON(404, "惩罚类型不存在", null);
            }

            return Response.toJSON(200, "获取惩罚类型详情成功", category);
        } catch (Exception e) {
            return Response.toJSON(500, "获取惩罚类型详情失败：" + e.getMessage(), null);
        }
    }

    /**
     * 新增惩罚类型（仅监督人可操作）
     * @param supervisorId 监督人ID
     * @param request 惩罚类型请求信息
     * @return 新增结果JSON字符串
     */
    public String createPunishmentCategory(Long supervisorId, PunishmentCategoryRequest request) {
        // 参数验证
        if (!request.isValid()) {
            if (!request.isValidCategoryName()) {
                return Response.toJSON(400, "类型名称不能为空且长度不能超过50个字符", null);
            }
            if (!request.isValidCategoryDesc()) {
                return Response.toJSON(400, "类型描述长度不能超过200个字符", null);
            }
        }

        try {
            // 检查类型名称是否重复
            PunishmentCategory existingCategory = punishmentCategoryMapper.findBySupervisorIdAndCategoryName(
                supervisorId, request.getCategoryName().trim());
            if (existingCategory != null) {
                return Response.toJSON(400, "类型名称已存在，请使用其他名称", null);
            }

            // 创建新的惩罚类型
            PunishmentCategory newCategory = new PunishmentCategory();
            newCategory.setSupervisorId(supervisorId);
            newCategory.setCategoryName(request.getCategoryName().trim());
            newCategory.setCategoryDesc(request.getCategoryDesc() != null ? request.getCategoryDesc().trim() : null);
            newCategory.setCreateTime(LocalDateTime.now());

            int insertResult = punishmentCategoryMapper.insert(newCategory);
            if (insertResult > 0) {
                return Response.toJSON(200, "惩罚类型创建成功", newCategory);
            } else {
                return Response.toJSON(500, "惩罚类型创建失败", null);
            }

        } catch (Exception e) {
            return Response.toJSON(500, "惩罚类型创建失败：" + e.getMessage(), null);
        }
    }

    /**
     * 编辑惩罚类型（仅监督人可操作）
     * @param categoryId 类型ID
     * @param supervisorId 监督人ID
     * @param request 惩罚类型请求信息
     * @return 编辑结果JSON字符串
     */
    public String updatePunishmentCategory(Long categoryId, Long supervisorId, PunishmentCategoryRequest request) {
        // 参数验证
        if (!request.isValid()) {
            if (!request.isValidCategoryName()) {
                return Response.toJSON(400, "类型名称不能为空且长度不能超过50个字符", null);
            }
            if (!request.isValidCategoryDesc()) {
                return Response.toJSON(400, "类型描述长度不能超过200个字符", null);
            }
        }

        try {
            // 检查惩罚类型是否存在且属于当前监督人
            PunishmentCategory existingCategory = punishmentCategoryMapper.findByCategoryId(categoryId);
            if (existingCategory == null) {
                return Response.toJSON(404, "惩罚类型不存在", null);
            }

            if (!existingCategory.getSupervisorId().equals(supervisorId)) {
                return Response.toJSON(403, "无权限编辑此惩罚类型", null);
            }

            // 检查类型名称是否与其他类型重复
            PunishmentCategory duplicateCategory = punishmentCategoryMapper.findBySupervisorIdAndCategoryNameExcludeId(
                supervisorId, request.getCategoryName().trim(), categoryId);
            if (duplicateCategory != null) {
                return Response.toJSON(400, "类型名称已存在，请使用其他名称", null);
            }

            // 更新惩罚类型
            int updateResult = punishmentCategoryMapper.update(
                categoryId,
                request.getCategoryName().trim(),
                request.getCategoryDesc() != null ? request.getCategoryDesc().trim() : null
            );

            if (updateResult > 0) {
                // 获取更新后的数据
                PunishmentCategory updatedCategory = punishmentCategoryMapper.findByCategoryId(categoryId);
                return Response.toJSON(200, "惩罚类型更新成功", updatedCategory);
            } else {
                return Response.toJSON(500, "惩罚类型更新失败", null);
            }

        } catch (Exception e) {
            return Response.toJSON(500, "惩罚类型更新失败：" + e.getMessage(), null);
        }
    }

    /**
     * 删除惩罚类型（仅监督人可操作）
     * @param categoryId 类型ID
     * @param supervisorId 监督人ID
     * @return 删除结果JSON字符串
     */
    public String deletePunishmentCategory(Long categoryId, Long supervisorId) {
        try {
            // 检查惩罚类型是否存在且属于当前监督人
            PunishmentCategory existingCategory = punishmentCategoryMapper.findByCategoryId(categoryId);
            if (existingCategory == null) {
                return Response.toJSON(404, "惩罚类型不存在", null);
            }

            if (!existingCategory.getSupervisorId().equals(supervisorId)) {
                return Response.toJSON(403, "无权限删除此惩罚类型", null);
            }

            // 检查是否被使用
            if (isPunishmentCategoryInUse(categoryId)) {
                return Response.toJSON(400, "该惩罚类型正在被使用，无法删除", null);
            }

            // 删除惩罚类型
            int deleteResult = punishmentCategoryMapper.delete(categoryId, supervisorId);
            if (deleteResult > 0) {
                return Response.toJSON(200, "惩罚类型删除成功", null);
            } else {
                return Response.toJSON(500, "惩罚类型删除失败", null);
            }

        } catch (Exception e) {
            return Response.toJSON(500, "惩罚类型删除失败：" + e.getMessage(), null);
        }
    }

    /**
     * 检查惩罚类型是否正在被使用
     * @param categoryId 类型ID
     * @return true=正在使用，false=未使用
     */
    private boolean isPunishmentCategoryInUse(Long categoryId) {
        try {
            // 检查在事项惩罚关联表中的使用情况
            int matterUsage = punishmentCategoryMapper.countUsageInMatterPunishments(categoryId);
            if (matterUsage > 0) {
                return true;
            }

            // 检查在计划惩罚关联表中的使用情况
            int planUsage = punishmentCategoryMapper.countUsageInPlanPunishments(categoryId);
            if (planUsage > 0) {
                return true;
            }

            // 检查在惩罚记录表中的使用情况
            int recordUsage = punishmentCategoryMapper.countUsageInPunishmentRecords(categoryId);
            if (recordUsage > 0) {
                return true;
            }

            // 检查是否被设置为默认惩罚类型
            int defaultUsage = punishmentCategoryMapper.countUsageAsDefaultPunishment(categoryId);
            if (defaultUsage > 0) {
                return true;
            }

            return false;
        } catch (Exception e) {
            // 如果检查过程中出现异常，为了安全起见，认为正在使用
            return true;
        }
    }
}
