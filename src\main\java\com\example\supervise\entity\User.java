package com.example.supervise.entity;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 用户实体类
 * 对应数据库表：users
 */
@Data
public class User {
    
    /**
     * 用户主键ID
     */
    private Long userId;
    
    /**
     * 用户账号（用于登录）
     */
    private String userAccount;
    
    /**
     * 用户名（显示名称）
     */
    private String username;
    
    /**
     * 密码哈希值
     */
    private String passwordHash;
    
    /**
     * 角色类型（1=监督人，2=被监督人）
     */
    private Integer roleType;
    
    /**
     * 头像URL
     */
    private String avatarUrl;
    
    /**
     * 个人简介
     */
    private String bio;
    
    /**
     * 注册时间
     */
    private LocalDateTime createTime;
    
    // 角色类型常量
    public static final int ROLE_SUPERVISOR = 1;    // 监督人
    public static final int ROLE_SUPERVISEE = 2;    // 被监督人
    
    /**
     * 判断是否为监督人
     */
    public boolean isSupervisor() {
        return ROLE_SUPERVISOR == this.roleType;
    }
    
    /**
     * 判断是否为被监督人
     */
    public boolean isSupervisee() {
        return ROLE_SUPERVISEE == this.roleType;
    }
    
    /**
     * 获取角色名称
     */
    public String getRoleName() {
        if (roleType == null) {
            return "未知";
        }
        switch (roleType) {
            case ROLE_SUPERVISOR:
                return "监督人";
            case ROLE_SUPERVISEE:
                return "被监督人";
            default:
                return "未知";
        }
    }
}
