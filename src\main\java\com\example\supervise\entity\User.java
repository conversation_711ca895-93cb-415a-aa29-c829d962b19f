package com.example.supervise.entity;

import java.time.LocalDateTime;

/**
 * 用户实体类
 * 对应数据库表：users
 */
public class User {

    /**
     * 用户主键ID
     */
    private Long userId;

    /**
     * 用户账号（用于登录）
     */
    private String userAccount;

    /**
     * 用户名（显示名称）
     */
    private String username;

    /**
     * 密码哈希值
     */
    private String passwordHash;

    /**
     * 角色类型（1=监督人，2=被监督人）
     */
    private Integer roleType;

    /**
     * 头像URL
     */
    private String avatarUrl;

    /**
     * 个人简介
     */
    private String bio;

    /**
     * 注册时间
     */
    private LocalDateTime createTime;

    // 角色类型常量
    public static final int ROLE_SUPERVISOR = 1;    // 监督人
    public static final int ROLE_SUPERVISEE = 2;    // 被监督人

    /**
     * 无参构造函数
     */
    public User() {
    }

    /**
     * 全参构造函数
     */
    public User(Long userId, String userAccount, String username, String passwordHash,
                Integer roleType, String avatarUrl, String bio, LocalDateTime createTime) {
        this.userId = userId;
        this.userAccount = userAccount;
        this.username = username;
        this.passwordHash = passwordHash;
        this.roleType = roleType;
        this.avatarUrl = avatarUrl;
        this.bio = bio;
        this.createTime = createTime;
    }

    /**
     * 获取用户主键ID
     */
    public Long getUserId() {
        return userId;
    }

    /**
     * 设置用户主键ID
     */
    public void setUserId(Long userId) {
        this.userId = userId;
    }

    /**
     * 获取用户账号
     */
    public String getUserAccount() {
        return userAccount;
    }

    /**
     * 设置用户账号
     */
    public void setUserAccount(String userAccount) {
        this.userAccount = userAccount;
    }

    /**
     * 获取用户名
     */
    public String getUsername() {
        return username;
    }

    /**
     * 设置用户名
     */
    public void setUsername(String username) {
        this.username = username;
    }

    /**
     * 获取密码哈希值
     */
    public String getPasswordHash() {
        return passwordHash;
    }

    /**
     * 设置密码哈希值
     */
    public void setPasswordHash(String passwordHash) {
        this.passwordHash = passwordHash;
    }

    /**
     * 获取角色类型
     */
    public Integer getRoleType() {
        return roleType;
    }

    /**
     * 设置角色类型
     */
    public void setRoleType(Integer roleType) {
        this.roleType = roleType;
    }

    /**
     * 获取头像URL
     */
    public String getAvatarUrl() {
        return avatarUrl;
    }

    /**
     * 设置头像URL
     */
    public void setAvatarUrl(String avatarUrl) {
        this.avatarUrl = avatarUrl;
    }

    /**
     * 获取个人简介
     */
    public String getBio() {
        return bio;
    }

    /**
     * 设置个人简介
     */
    public void setBio(String bio) {
        this.bio = bio;
    }

    /**
     * 获取注册时间
     */
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    /**
     * 设置注册时间
     */
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    /**
     * 判断是否为监督人
     */
    public boolean isSupervisor() {
        return ROLE_SUPERVISOR == this.roleType;
    }

    /**
     * 判断是否为被监督人
     */
    public boolean isSupervisee() {
        return ROLE_SUPERVISEE == this.roleType;
    }

    /**
     * 获取角色名称
     */
    public String getRoleName() {
        if (roleType == null) {
            return "未知";
        }
        switch (roleType) {
            case ROLE_SUPERVISOR:
                return "监督人";
            case ROLE_SUPERVISEE:
                return "被监督人";
            default:
                return "未知";
        }
    }

    /**
     * toString方法
     */
    @Override
    public String toString() {
        return "User{" +
                "userId=" + userId +
                ", userAccount='" + userAccount + '\'' +
                ", username='" + username + '\'' +
                ", passwordHash='" + passwordHash + '\'' +
                ", roleType=" + roleType +
                ", avatarUrl='" + avatarUrl + '\'' +
                ", bio='" + bio + '\'' +
                ", createTime=" + createTime +
                '}';
    }

    /**
     * equals方法
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        User user = (User) obj;
        return userId != null && userId.equals(user.userId);
    }

    /**
     * hashCode方法
     */
    @Override
    public int hashCode() {
        return userId != null ? userId.hashCode() : 0;
    }
}
