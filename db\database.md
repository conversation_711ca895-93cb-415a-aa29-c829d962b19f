# 数据库设计

## 1.用户表（users）
CREATE TABLE users (
    user_id       BIGINT       PRIMARY KEY AUTO_INCREMENT COMMENT '用户主键ID',
    user_account  VARCHAR(50)  NOT NULL UNIQUE COMMENT '用户账号（用于登录）',
    username      VARCHAR(50)  NOT NULL COMMENT '用户名（显示名称）',
    password_hash VARCHAR(100) NOT NULL COMMENT '密码哈希值',
    role_type     TINYINT      NOT NULL COMMENT '角色类型（1=监督人，2=被监督人）',
    avatar_url    VARCHAR(255) NULL COMMENT '头像URL',
    bio           TEXT         NULL COMMENT '个人简介',
    create_time   DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '注册时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户信息表（含登录账号）';

## 2. 监督关系表（supervision_relation）
CREATE TABLE supervision_relation (
    relation_id                 BIGINT       PRIMARY KEY AUTO_INCREMENT COMMENT '关系主键ID',
    supervisor_id               BIGINT       NOT NULL COMMENT '监督人ID（外键）',
    supervisee_id               BIGINT       NOT NULL COMMENT '被监督人ID（外键）',
    default_punishment_category BIGINT       NULL COMMENT '默认惩罚类型ID（外键）',
    default_punishment_count    INT          NOT NULL DEFAULT 1 COMMENT '默认惩罚数量',
    create_time                 DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '关联创建时间',
    UNIQUE KEY uk_supervisor_supervisee (supervisor_id, supervisee_id),
    FOREIGN KEY (supervisor_id) REFERENCES users(user_id),
    FOREIGN KEY (supervisee_id) REFERENCES users(user_id),
    FOREIGN KEY (default_punishment_category) REFERENCES punishment_categories(category_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='监督关联关系表';

## 3. 惩罚类型表（punishment_categories）
CREATE TABLE punishment_categories (
    category_id   BIGINT       PRIMARY KEY AUTO_INCREMENT COMMENT '类型主键ID',
    supervisor_id BIGINT       NOT NULL COMMENT '监督人ID（外键）',
    category_name VARCHAR(50)  NOT NULL COMMENT '类型名称（如"运动"、"学习"）',
    category_desc VARCHAR(200) NULL COMMENT '类型描述',
    create_time   DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (supervisor_id) REFERENCES users(user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='惩罚类型表';

## 4. 事项表（matters）
CREATE TABLE matters (
    matter_id        BIGINT       PRIMARY KEY AUTO_INCREMENT COMMENT '事项主键ID',
    supervisee_id    BIGINT       NOT NULL COMMENT '被监督人ID（外键）',
    relation_id      BIGINT       NOT NULL COMMENT '监督关系ID（外键）',
    title            VARCHAR(100) NOT NULL COMMENT '事项标题',
    content          TEXT         NOT NULL COMMENT '事项内容',
    end_time         DATETIME     NOT NULL COMMENT '预计完成时间',
    approval_status  TINYINT      NOT NULL DEFAULT 1 COMMENT '审批状态（1=待审批，2=同意，3=驳回）',
    approval_opinion VARCHAR(200) NULL COMMENT '审批意见',
    approval_time    DATETIME     NULL COMMENT '审批时间',
    create_time      DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '提交时间',
    FOREIGN KEY (supervisee_id) REFERENCES users(user_id),
    FOREIGN KEY (relation_id) REFERENCES supervision_relation(relation_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='事项审批表（移除预计开始时间）';

## 5. 事项惩罚关联表（matter_punishments）
CREATE TABLE matter_punishments (
    matter_punish_id BIGINT       PRIMARY KEY AUTO_INCREMENT COMMENT '关联主键ID',
    matter_id        BIGINT       NOT NULL COMMENT '事项ID（外键）',
    category_id      BIGINT       NOT NULL COMMENT '惩罚类型ID（外键）',
    punishment_count INT          NOT NULL DEFAULT 1 COMMENT '该类型惩罚数量',
    UNIQUE KEY uk_matter_category (matter_id, category_id),
    FOREIGN KEY (matter_id) REFERENCES matters(matter_id),
    FOREIGN KEY (category_id) REFERENCES punishment_categories(category_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='事项与惩罚类型关联表';

## 6. 计划表（plans）
CREATE TABLE plans (
    plan_id          BIGINT       PRIMARY KEY AUTO_INCREMENT COMMENT '计划主键ID',
    relation_id      BIGINT       NOT NULL COMMENT '监督关系ID（外键）',
    plan_type        TINYINT      NOT NULL COMMENT '计划类型（1=自定计划，2=监督计划）',
    title            VARCHAR(100) NOT NULL COMMENT '计划标题',
    plan_content     TEXT         NOT NULL COMMENT '计划内容',
    due_time         DATETIME     NOT NULL COMMENT '完成时间节点',
    status           TINYINT      NOT NULL DEFAULT 1 COMMENT '状态（1=待完成，2=已提交，3=未完成）',
    create_time      DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (relation_id) REFERENCES supervision_relation(relation_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='计划表';

## 7. 计划惩罚关联表（plan_punishments）
CREATE TABLE plan_punishments (
    plan_punish_id   BIGINT       PRIMARY KEY AUTO_INCREMENT COMMENT '关联主键ID',
    plan_id          BIGINT       NOT NULL COMMENT '计划ID（外键）',
    category_id      BIGINT       NOT NULL COMMENT '惩罚类型ID（外键）',
    punishment_count INT          NOT NULL DEFAULT 1 COMMENT '该类型惩罚数量',
    UNIQUE KEY uk_plan_category (plan_id, category_id),
    FOREIGN KEY (plan_id) REFERENCES plans(plan_id),
    FOREIGN KEY (category_id) REFERENCES punishment_categories(category_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='计划与惩罚类型关联表';

## 8. 日记表（diaries）
CREATE TABLE diaries (
    diary_id       BIGINT       PRIMARY KEY AUTO_INCREMENT COMMENT '日记主键ID',
    supervisee_id  BIGINT       NOT NULL COMMENT '被监督人ID（外键）',
    relation_id    BIGINT       NOT NULL COMMENT '监督关系ID（外键）',
    diary_date     DATE         NOT NULL COMMENT '日记日期',
    content        TEXT         NOT NULL COMMENT '日记内容',
    submit_time    DATETIME     NULL COMMENT '提交时间',
    status         TINYINT      NOT NULL DEFAULT 1 COMMENT '状态（1=未提交，2=已提交，3=未完成）',
    create_time    DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    UNIQUE KEY uk_relation_date (relation_id, diary_date),
    FOREIGN KEY (supervisee_id) REFERENCES users(user_id),
    FOREIGN KEY (relation_id) REFERENCES supervision_relation(relation_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='日记表';

## 9. 日记图片表（diary_images）
CREATE TABLE diary_images (
    image_id   BIGINT       PRIMARY KEY AUTO_INCREMENT COMMENT '图片主键ID',
    diary_id   BIGINT       NOT NULL COMMENT '日记ID（外键）',
    image_url  VARCHAR(255) NOT NULL COMMENT '图片URL',
    create_time DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
    FOREIGN KEY (diary_id) REFERENCES diaries(diary_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='日记图片表';

## 10. 惩罚记录表（punishment_records）
CREATE TABLE punishment_records (
    record_id      BIGINT       PRIMARY KEY AUTO_INCREMENT COMMENT '惩罚记录主键ID',
    relation_id    BIGINT       NOT NULL COMMENT '监督关系ID（外键）',
    source_type    TINYINT      NOT NULL COMMENT '来源类型（1=事项，2=计划，3=日记，4=手动）',
    source_id      BIGINT       NOT NULL COMMENT '来源ID（事项ID/计划ID/日记ID）',
    category_id    BIGINT       NOT NULL COMMENT '惩罚类型ID（外键）',
    change_type    TINYINT      NOT NULL COMMENT '数量变化类型（1=增加，2=减少，3=清零）',
    change_count   INT          NOT NULL COMMENT '变化数量（正数增加，负数减少）',
    operator_id    BIGINT       NOT NULL COMMENT '操作人ID（监督人或系统）',
    operate_time   DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    reason         VARCHAR(200) NULL COMMENT '操作原因',
    FOREIGN KEY (relation_id) REFERENCES supervision_relation(relation_id),
    FOREIGN KEY (category_id) REFERENCES punishment_categories(category_id),
    FOREIGN KEY (operator_id) REFERENCES users(user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='惩罚数量变化记录表';


