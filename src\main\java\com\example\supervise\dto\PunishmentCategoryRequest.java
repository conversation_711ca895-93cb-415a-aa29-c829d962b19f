package com.example.supervise.dto;

/**
 * 惩罚类型请求DTO
 * 用于接收前端传递的惩罚类型信息（新增和编辑）
 */
public class PunishmentCategoryRequest {

    /**
     * 类型名称（如"运动"、"学习"）
     */
    private String categoryName;

    /**
     * 类型描述
     */
    private String categoryDesc;

    /**
     * 无参构造函数
     */
    public PunishmentCategoryRequest() {
    }

    /**
     * 全参构造函数
     */
    public PunishmentCategoryRequest(String categoryName, String categoryDesc) {
        this.categoryName = categoryName;
        this.categoryDesc = categoryDesc;
    }

    /**
     * 获取类型名称
     */
    public String getCategoryName() {
        return categoryName;
    }

    /**
     * 设置类型名称
     */
    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    /**
     * 获取类型描述
     */
    public String getCategoryDesc() {
        return categoryDesc;
    }

    /**
     * 设置类型描述
     */
    public void setCategoryDesc(String categoryDesc) {
        this.categoryDesc = categoryDesc;
    }

    /**
     * 验证类型名称是否有效
     * @return true=有效，false=无效
     */
    public boolean isValidCategoryName() {
        return categoryName != null && !categoryName.trim().isEmpty() && categoryName.trim().length() <= 50;
    }

    /**
     * 验证类型描述是否有效
     * @return true=有效，false=无效
     */
    public boolean isValidCategoryDesc() {
        return categoryDesc == null || categoryDesc.length() <= 200;
    }

    /**
     * 验证所有字段是否有效
     * @return true=所有字段有效，false=存在无效字段
     */
    public boolean isValid() {
        return isValidCategoryName() && isValidCategoryDesc();
    }

    /**
     * toString方法
     */
    @Override
    public String toString() {
        return "PunishmentCategoryRequest{" +
                "categoryName='" + categoryName + '\'' +
                ", categoryDesc='" + categoryDesc + '\'' +
                '}';
    }
}
