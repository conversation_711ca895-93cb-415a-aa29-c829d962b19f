package com.example.supervise.config;

import com.example.supervise.filter.JwtAuthenticationFilter;
import com.example.supervise.util.JwtUtil;
import com.example.supervise.util.Response;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;

/**
 * Spring Security安全配置类
 * 用于配置监督网站的安全规则、权限控制和CORS跨域设置
 */
@Configuration
@EnableWebSecurity
public class SecurityConfig {

    /**
     * JWT工具类，用于令牌验证
     */
    private final JwtUtil jwtUtil;

    /**
     * 从配置文件中读取允许的跨域源
     */
    @Value("${cors.allowed-origins}")
    private String[] allowedOrigins;

    /**
     * 构造函数，通过依赖注入获取JwtUtil
     * @param jwtUtil JWT工具类实例
     */
    public SecurityConfig(JwtUtil jwtUtil) {
        this.jwtUtil = jwtUtil;
    }

    /**
     * 配置Spring Security的安全过滤链
     * 这是整个安全配置的核心方法
     * @param http HttpSecurity配置对象
     * @return SecurityFilterChain 安全过滤链
     * @throws Exception 配置异常
     */
    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        http
                // 禁用CSRF保护（因为使用JWT，不需要CSRF保护）
                .csrf(csrf -> csrf.disable())
                
                // 启用CORS跨域支持
                .cors(cors -> cors.configurationSource(corsConfigurationSource()))
                
                // 配置Session管理策略为无状态（JWT不需要Session）
                .sessionManagement(session -> session
                        .sessionCreationPolicy(SessionCreationPolicy.STATELESS)
                )
                
                // 配置异常处理器
                .exceptionHandling(exception -> exception
                        // 配置认证入口点：当用户未认证时的处理逻辑
                        .authenticationEntryPoint((request, response, authException) -> {
                            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                            response.setContentType("application/json;charset=UTF-8");
                            String errorResponse = Response.toJSON(401, "未授权访问：请先登录", null);
                            response.getWriter().write(errorResponse);
                        })
                        
                        // 配置访问拒绝处理器：当用户权限不足时的处理逻辑
                        .accessDeniedHandler((request, response, accessDeniedException) -> {
                            response.setStatus(HttpServletResponse.SC_FORBIDDEN);
                            response.setContentType("application/json;charset=UTF-8");
                            String errorResponse = Response.toJSON(403, "权限不足：无法访问该资源", null);
                            response.getWriter().write(errorResponse);
                        })
                )
                
                // 配置URL访问权限控制
                .authorizeHttpRequests(authorize -> authorize
                        // === 公开接口：无需认证即可访问 ===
                        .requestMatchers("/api/public/**").permitAll()           // 公开API接口
                        .requestMatchers("/api/auth/**").permitAll()             // 认证相关接口（登录、注册）
                        .requestMatchers("/uploads/**").permitAll()              // 文件上传路径
                        .requestMatchers("/static/**").permitAll()               // 静态资源
                        .requestMatchers("/error").permitAll()                   // 错误页面
                        .requestMatchers("/favicon.ico").permitAll()             // 网站图标
                        
                        // === 监督人专用接口：只有监督人可以访问 ===
                        .requestMatchers("/api/supervisor/**").hasRole("SUPERVISOR")
                        
                        // === 被监督人专用接口：只有被监督人可以访问 ===
                        .requestMatchers("/api/supervisee/**").hasRole("SUPERVISEE")
                        
                        // === 共享接口：监督人和被监督人都可以访问 ===
                        .requestMatchers("/api/shared/**").hasAnyRole("SUPERVISOR", "SUPERVISEE")
                        
                        // === 用户相关接口：所有已认证用户都可以访问 ===
                        .requestMatchers("/api/user/**").hasAnyRole("SUPERVISOR", "SUPERVISEE")
                        
                        // === 其他所有请求都需要认证 ===
                        .anyRequest().authenticated()
                )
                
                // 添加JWT认证过滤器到Spring Security过滤链中
                // 将其放在UsernamePasswordAuthenticationFilter之前执行
                .addFilterBefore(new JwtAuthenticationFilter(jwtUtil), UsernamePasswordAuthenticationFilter.class);

        return http.build();
    }

    /**
     * 配置CORS过滤器Bean
     * 用于处理跨域请求
     * @return CorsFilter CORS过滤器实例
     */
    @Bean
    public CorsFilter corsFilter() {
        CorsConfiguration config = new CorsConfiguration();
        
        // 设置允许的域名（从配置文件读取）
        for (String origin : allowedOrigins) {
            config.addAllowedOriginPattern(origin);
        }
        
        // 允许所有请求头
        config.addAllowedHeader("*");
        
        // 允许所有HTTP方法（GET、POST、PUT、DELETE等）
        config.addAllowedMethod("*");
        
        // 允许发送Cookie和认证信息
        config.setAllowCredentials(true);

        // 创建CORS配置源
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", config);

        return new CorsFilter(source);
    }

    /**
     * 创建CORS配置源
     * 为Spring Security提供CORS配置
     * @return CorsConfigurationSource CORS配置源
     */
    private CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration config = new CorsConfiguration();
        
        // 设置允许的域名（从配置文件读取）
        for (String origin : allowedOrigins) {
            config.addAllowedOriginPattern(origin);
        }
        
        // 允许所有请求头
        config.addAllowedHeader("*");
        
        // 允许所有HTTP方法
        config.addAllowedMethod("*");
        
        // 允许发送Cookie和认证信息
        config.setAllowCredentials(true);

        // 创建基于URL的CORS配置源
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        // 为所有路径应用CORS配置
        source.registerCorsConfiguration("/**", config);

        return source;
    }
}
