package com.example.supervise.controller;

import com.example.supervise.dto.LoginRequest;
import com.example.supervise.service.UserService;
import com.example.supervise.util.JwtUtil;
import com.example.supervise.util.Response;
import org.springframework.web.bind.annotation.*;

/**
 * 认证控制器
 * 处理用户认证相关的接口请求
 */
@RestController
@RequestMapping("/api/auth")
public class AuthController {

    private final UserService userService;
    private final JwtUtil jwtUtil;

    /**
     * 构造函数，注入依赖服务
     */
    public AuthController(UserService userService, JwtUtil jwtUtil) {
        this.userService = userService;
        this.jwtUtil = jwtUtil;
    }

    /**
     * 用户登录接口
     * @param loginRequest 登录请求信息
     * @return 登录结果JSON字符串
     */
    @PostMapping("/login")
    public String login(@RequestBody LoginRequest loginRequest) {
        return userService.login(loginRequest);
    }

    /**
     * 用户退出登录接口
     * @param token JWT令牌（从请求头获取）
     * @return 退出结果JSON字符串
     */
    @PostMapping("/logout")
    public String logout(@RequestHeader("Authorization") String token) {
        try {
            // 提取JWT令牌（去掉"Bearer "前缀）
            if (token != null && token.startsWith("Bearer ")) {
                String jwtToken = token.substring(7);
                
                // 使令牌失效
                jwtUtil.invalidateToken(jwtToken);
                
                return Response.toJSON(200, "退出登录成功", null);
            } else {
                return Response.toJSON(400, "无效的令牌格式", null);
            }
        } catch (Exception e) {
            return Response.toJSON(500, "退出登录失败：" + e.getMessage(), null);
        }
    }

    /**
     * 验证令牌有效性接口
     * @param token JWT令牌（从请求头获取）
     * @return 验证结果JSON字符串
     */
    @GetMapping("/validate")
    public String validateToken(@RequestHeader("Authorization") String token) {
        try {
            // 提取JWT令牌（去掉"Bearer "前缀）
            if (token != null && token.startsWith("Bearer ")) {
                String jwtToken = token.substring(7);
                
                // 验证令牌
                int validationResult = jwtUtil.validateToken(jwtToken);
                
                if (validationResult == 0) {
                    // 令牌有效，返回用户信息
                    Long userId = jwtUtil.getUserIdFromToken(jwtToken);
                    String userAccount = jwtUtil.getUserAccountFromToken(jwtToken);
                    String role = jwtUtil.getRoleFromToken(jwtToken);
                    
                    return Response.toJSON(200, "令牌验证成功", 
                        new Object[]{
                            "userId=" + userId,
                            "userAccount=" + userAccount,
                            "role=" + role
                        });
                } else {
                    String errorMessage = getValidationErrorMessage(validationResult);
                    return Response.toJSON(401, errorMessage, null);
                }
            } else {
                return Response.toJSON(400, "无效的令牌格式", null);
            }
        } catch (Exception e) {
            return Response.toJSON(500, "令牌验证失败：" + e.getMessage(), null);
        }
    }

    /**
     * 根据验证结果码获取错误信息
     * @param validationResult 验证结果码
     * @return 错误信息
     */
    private String getValidationErrorMessage(int validationResult) {
        switch (validationResult) {
            case 1001:
                return "令牌为空";
            case 1002:
                return "令牌无效或已过期";
            case 1003:
                return "令牌已失效";
            default:
                return "令牌验证失败";
        }
    }
}
