# 监督关系功能实现说明

## 功能概述

已成功实现被监督人查看惩罚类型的功能，通过监督关系表（supervision_relation）进行查询。

## 实现逻辑

### 1. 数据库表结构
```sql
-- 监督关系表
CREATE TABLE supervision_relation (
    relation_id                 BIGINT       PRIMARY KEY AUTO_INCREMENT,
    supervisor_id               BIGINT       NOT NULL,     -- 监督人ID
    supervisee_id               BIGINT       NOT NULL,     -- 被监督人ID
    default_punishment_category BIGINT       NULL,         -- 默认惩罚类型ID
    default_punishment_count    INT          NOT NULL DEFAULT 1,
    create_time                 DATETIME     NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY uk_supervisor_supervisee (supervisor_id, supervisee_id)
);
```

### 2. 业务逻辑流程

#### 监督人访问惩罚类型：
1. 获取当前用户ID和角色
2. 确认用户角色为"supervisor"
3. 直接使用当前用户ID作为监督人ID
4. 查询该监督人创建的所有惩罚类型

#### 被监督人访问惩罚类型：
1. 获取当前用户ID和角色
2. 确认用户角色为"supervisee"
3. 通过监督关系表查询：`SELECT supervisor_id FROM supervision_relation WHERE supervisee_id = ?`
4. 获取对应的监督人ID
5. 查询该监督人创建的所有惩罚类型

### 3. 核心代码实现

```java
public String getPunishmentCategories(Long currentUserId, String currentUserRole) {
    try {
        Long supervisorId;
        
        if ("supervisor".equals(currentUserRole)) {
            // 监督人：直接使用当前用户ID
            supervisorId = currentUserId;
        } else if ("supervisee".equals(currentUserRole)) {
            // 被监督人：通过监督关系获取监督人ID
            SupervisionRelation relation = supervisionRelationMapper.findBySuperviseeId(currentUserId);
            if (relation == null) {
                return Response.toJSON(404, "未找到相关的监督关系", null);
            }
            supervisorId = relation.getSupervisorId();
        } else {
            return Response.toJSON(400, "无效的用户角色", null);
        }
        
        List<PunishmentCategory> categories = punishmentCategoryMapper.findBySupervisorId(supervisorId);
        return Response.toJSON(200, "获取惩罚类型列表成功", categories);
    } catch (Exception e) {
        return Response.toJSON(500, "获取惩罚类型列表失败：" + e.getMessage(), null);
    }
}
```

## 错误处理

### 1. 被监督人未建立监督关系
- **错误码**: 404
- **错误信息**: "未找到相关的监督关系"
- **场景**: 被监督人尚未与任何监督人建立关系

### 2. 无效的用户角色
- **错误码**: 400
- **错误信息**: "无效的用户角色"
- **场景**: 用户角色既不是监督人也不是被监督人

### 3. 系统异常
- **错误码**: 500
- **错误信息**: "获取惩罚类型列表失败：具体错误信息"
- **场景**: 数据库连接异常、SQL执行错误等

## 安全性考虑

1. **权限隔离**: 被监督人只能看到自己相关监督人的惩罚类型
2. **数据验证**: 严格验证用户身份和角色
3. **异常处理**: 完善的错误处理机制，避免敏感信息泄露

## 扩展性

1. **多监督人支持**: 当前设计支持一个被监督人对应一个监督人，如需支持多监督人，可修改查询逻辑
2. **权限细化**: 可以在监督关系表中添加权限字段，实现更细粒度的权限控制
3. **关系管理**: 已创建SupervisionRelationService，可扩展监督关系的增删改查功能

## 测试建议

1. **正常流程测试**:
   - 监督人登录后查看惩罚类型
   - 被监督人登录后查看惩罚类型

2. **异常流程测试**:
   - 被监督人未建立监督关系时的访问
   - 无效角色用户的访问
   - 数据库异常情况的处理

3. **权限测试**:
   - 验证被监督人只能看到相关监督人的惩罚类型
   - 验证监督人只能看到自己创建的惩罚类型
