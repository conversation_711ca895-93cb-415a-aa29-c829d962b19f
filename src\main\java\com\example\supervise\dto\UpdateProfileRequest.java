package com.example.supervise.dto;

/**
 * 用户信息更新请求DTO
 * 用于接收前端传递的用户信息更新数据
 */
public class UpdateProfileRequest {
    
    /**
     * 用户名（显示名称）
     */
    private String username;
    
    /**
     * 个人简介（不超过200字）
     */
    private String bio;
    
    /**
     * 无参构造函数
     */
    public UpdateProfileRequest() {
    }
    
    /**
     * 全参构造函数
     */
    public UpdateProfileRequest(String username, String bio) {
        this.username = username;
        this.bio = bio;
    }
    
    /**
     * 获取用户名
     */
    public String getUsername() {
        return username;
    }
    
    /**
     * 设置用户名
     */
    public void setUsername(String username) {
        this.username = username;
    }
    
    /**
     * 获取个人简介
     */
    public String getBio() {
        return bio;
    }
    
    /**
     * 设置个人简介
     */
    public void setBio(String bio) {
        this.bio = bio;
    }
    
    /**
     * 验证个人简介长度
     * @return true=长度合法，false=长度超限
     */
    public boolean isValidBio() {
        return bio == null || bio.length() <= 200;
    }
    
    /**
     * toString方法
     */
    @Override
    public String toString() {
        return "UpdateProfileRequest{" +
                "username='" + username + '\'' +
                ", bio='" + bio + '\'' +
                '}';
    }
}
