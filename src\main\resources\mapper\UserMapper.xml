<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.example.supervise.mapper.UserMapper">

    <!-- 用户结果映射 -->
    <resultMap id="UserResultMap" type="com.example.supervise.entity.User">
        <id property="userId" column="user_id"/>
        <result property="userAccount" column="user_account"/>
        <result property="username" column="username"/>
        <result property="passwordHash" column="password_hash"/>
        <result property="roleType" column="role_type"/>
        <result property="avatarUrl" column="avatar_url"/>
        <result property="bio" column="bio"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <!-- 根据用户账号查询用户信息 -->
    <select id="findByUserAccount" resultMap="UserResultMap">
        SELECT 
            user_id,
            user_account,
            username,
            password_hash,
            role_type,
            avatar_url,
            bio,
            create_time
        FROM users 
        WHERE user_account = #{userAccount}
    </select>

    <!-- 根据用户ID查询用户信息 -->
    <select id="findByUserId" resultMap="UserResultMap">
        SELECT 
            user_id,
            user_account,
            username,
            password_hash,
            role_type,
            avatar_url,
            bio,
            create_time
        FROM users 
        WHERE user_id = #{userId}
    </select>

    <!-- 更新用户头像 -->
    <update id="updateAvatar">
        UPDATE users 
        SET avatar_url = #{avatarUrl}
        WHERE user_id = #{userId}
    </update>

    <!-- 更新用户基本信息（用户名和个人简介） -->
    <update id="updateProfile">
        UPDATE users 
        SET username = #{username},
            bio = #{bio}
        WHERE user_id = #{userId}
    </update>

    <!-- 检查用户账号是否存在（排除指定用户ID） -->
    <select id="countByUserAccountExcludeUserId" resultType="int">
        SELECT COUNT(*) 
        FROM users 
        WHERE user_account = #{userAccount}
        <if test="excludeUserId != null">
            AND user_id != #{excludeUserId}
        </if>
    </select>

</mapper>
