package com.example.supervise.entity;

import java.time.LocalDateTime;

/**
 * 惩罚类型实体类
 * 对应数据库表：punishment_categories
 */
public class PunishmentCategory {

    /**
     * 类型主键ID
     */
    private Long categoryId;

    /**
     * 监督人ID（外键）
     */
    private Long supervisorId;

    /**
     * 类型名称（如"运动"、"学习"）
     */
    private String categoryName;

    /**
     * 类型描述
     */
    private String categoryDesc;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 无参构造函数
     */
    public PunishmentCategory() {
    }

    /**
     * 全参构造函数
     */
    public PunishmentCategory(Long categoryId, Long supervisorId, String categoryName, 
                             String categoryDesc, LocalDateTime createTime) {
        this.categoryId = categoryId;
        this.supervisorId = supervisorId;
        this.categoryName = categoryName;
        this.categoryDesc = categoryDesc;
        this.createTime = createTime;
    }

    /**
     * 获取类型主键ID
     */
    public Long getCategoryId() {
        return categoryId;
    }

    /**
     * 设置类型主键ID
     */
    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    /**
     * 获取监督人ID
     */
    public Long getSupervisorId() {
        return supervisorId;
    }

    /**
     * 设置监督人ID
     */
    public void setSupervisorId(Long supervisorId) {
        this.supervisorId = supervisorId;
    }

    /**
     * 获取类型名称
     */
    public String getCategoryName() {
        return categoryName;
    }

    /**
     * 设置类型名称
     */
    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    /**
     * 获取类型描述
     */
    public String getCategoryDesc() {
        return categoryDesc;
    }

    /**
     * 设置类型描述
     */
    public void setCategoryDesc(String categoryDesc) {
        this.categoryDesc = categoryDesc;
    }

    /**
     * 获取创建时间
     */
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     */
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    /**
     * toString方法
     */
    @Override
    public String toString() {
        return "PunishmentCategory{" +
                "categoryId=" + categoryId +
                ", supervisorId=" + supervisorId +
                ", categoryName='" + categoryName + '\'' +
                ", categoryDesc='" + categoryDesc + '\'' +
                ", createTime=" + createTime +
                '}';
    }

    /**
     * equals方法
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        PunishmentCategory that = (PunishmentCategory) obj;
        return categoryId != null && categoryId.equals(that.categoryId);
    }

    /**
     * hashCode方法
     */
    @Override
    public int hashCode() {
        return categoryId != null ? categoryId.hashCode() : 0;
    }
}
