package com.example.supervise.controller;

import com.example.supervise.util.Response;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 测试控制器
 * 用于验证Security配置和JWT认证是否正常工作
 */
@RestController
public class TestController {

    /**
     * 公开接口测试 - 无需认证
     */
    @GetMapping("/api/public/test")
    public String publicTest() {
        return Response.toJSON(200, "公开接口访问成功", "这是一个无需认证的公开接口");
    }

    /**
     * 认证接口测试 - 需要登录
     */
    @GetMapping("/api/auth/test")
    public String authTest() {
        return Response.toJSON(200, "认证接口访问成功", "这是一个认证相关的接口");
    }

    /**
     * 监督人专用接口测试
     */
    @GetMapping("/api/supervisor/test")
    public String supervisorTest() {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        return Response.toJSON(200, "监督人接口访问成功", 
            "当前用户：" + auth.getName() + "，权限：" + auth.getAuthorities());
    }

    /**
     * 被监督人专用接口测试
     */
    @GetMapping("/api/supervisee/test")
    public String superviseeTest() {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        return Response.toJSON(200, "被监督人接口访问成功", 
            "当前用户：" + auth.getName() + "，权限：" + auth.getAuthorities());
    }

    /**
     * 共享接口测试 - 监督人和被监督人都可以访问
     */
    @GetMapping("/api/shared/test")
    public String sharedTest() {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        return Response.toJSON(200, "共享接口访问成功", 
            "当前用户：" + auth.getName() + "，权限：" + auth.getAuthorities());
    }

    /**
     * 用户接口测试 - 所有已认证用户都可以访问
     */
    @GetMapping("/api/user/profile")
    public String userProfile() {
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        return Response.toJSON(200, "用户信息获取成功", 
            "当前用户：" + auth.getName() + "，用户ID：" + auth.getDetails());
    }
}
