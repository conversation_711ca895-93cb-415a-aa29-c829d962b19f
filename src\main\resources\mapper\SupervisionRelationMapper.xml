<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.example.supervise.mapper.SupervisionRelationMapper">

    <!-- 监督关系结果映射 -->
    <resultMap id="SupervisionRelationResultMap" type="com.example.supervise.entity.SupervisionRelation">
        <id property="relationId" column="relation_id"/>
        <result property="supervisorId" column="supervisor_id"/>
        <result property="superviseeId" column="supervisee_id"/>
        <result property="defaultPunishmentCategory" column="default_punishment_category"/>
        <result property="defaultPunishmentCount" column="default_punishment_count"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <!-- 根据被监督人ID查询监督关系 -->
    <select id="findBySuperviseeId" resultMap="SupervisionRelationResultMap">
        SELECT 
            relation_id,
            supervisor_id,
            supervisee_id,
            default_punishment_category,
            default_punishment_count,
            create_time
        FROM supervision_relation 
        WHERE supervisee_id = #{superviseeId}
    </select>

    <!-- 根据监督人ID查询所有监督关系 -->
    <select id="findBySupervisorId" resultMap="SupervisionRelationResultMap">
        SELECT 
            relation_id,
            supervisor_id,
            supervisee_id,
            default_punishment_category,
            default_punishment_count,
            create_time
        FROM supervision_relation 
        WHERE supervisor_id = #{supervisorId}
        ORDER BY create_time DESC
    </select>

    <!-- 根据监督人ID和被监督人ID查询监督关系 -->
    <select id="findBySupervisorIdAndSuperviseeId" resultMap="SupervisionRelationResultMap">
        SELECT 
            relation_id,
            supervisor_id,
            supervisee_id,
            default_punishment_category,
            default_punishment_count,
            create_time
        FROM supervision_relation 
        WHERE supervisor_id = #{supervisorId} 
        AND supervisee_id = #{superviseeId}
    </select>

    <!-- 根据关系ID查询监督关系 -->
    <select id="findByRelationId" resultMap="SupervisionRelationResultMap">
        SELECT 
            relation_id,
            supervisor_id,
            supervisee_id,
            default_punishment_category,
            default_punishment_count,
            create_time
        FROM supervision_relation 
        WHERE relation_id = #{relationId}
    </select>

    <!-- 新增监督关系 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="relationId">
        INSERT INTO supervision_relation (
            supervisor_id,
            supervisee_id,
            default_punishment_category,
            default_punishment_count,
            create_time
        ) VALUES (
            #{supervisorId},
            #{superviseeId},
            #{defaultPunishmentCategory},
            #{defaultPunishmentCount},
            #{createTime}
        )
    </insert>

    <!-- 更新监督关系的默认惩罚设置 -->
    <update id="updateDefaultPunishment">
        UPDATE supervision_relation 
        SET default_punishment_category = #{defaultPunishmentCategory},
            default_punishment_count = #{defaultPunishmentCount}
        WHERE relation_id = #{relationId}
    </update>

    <!-- 删除监督关系 -->
    <delete id="delete">
        DELETE FROM supervision_relation 
        WHERE relation_id = #{relationId} 
        AND supervisor_id = #{supervisorId}
    </delete>

    <!-- 检查监督关系是否存在 -->
    <select id="countBySupervisorIdAndSuperviseeId" resultType="int">
        SELECT COUNT(*) 
        FROM supervision_relation 
        WHERE supervisor_id = #{supervisorId} 
        AND supervisee_id = #{superviseeId}
    </select>

</mapper>
