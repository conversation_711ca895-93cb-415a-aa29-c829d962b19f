# 监督网站API接口文档

## 认证相关接口

### 1. 用户登录
- **接口地址**: `POST /api/auth/login`
- **请求参数**:
```json
{
    "userAccount": "用户账号",
    "password": "密码"
}
```
- **成功响应**:
```json
{
    "code": 200,
    "message": "登录成功",
    "data": {
        "token": "JWT令牌",
        "userInfo": {
            "userId": 1,
            "userAccount": "test001",
            "username": "测试用户",
            "roleType": 1,
            "roleName": "监督人",
            "avatarUrl": "/uploads/avatars/avatar.jpg",
            "bio": "个人简介",
            "createTime": "2024-01-01 10:00:00"
        }
    }
}
```

### 2. 用户退出登录
- **接口地址**: `POST /api/auth/logout`
- **请求头**: `Authorization: Bearer {token}`
- **成功响应**:
```json
{
    "code": 200,
    "message": "退出登录成功",
    "data": null
}
```

### 3. 验证令牌
- **接口地址**: `GET /api/auth/validate`
- **请求头**: `Authorization: Bearer {token}`
- **成功响应**:
```json
{
    "code": 200,
    "message": "令牌验证成功",
    "data": ["userId=1", "userAccount=test001", "role=supervisor"]
}
```

## 用户信息管理接口

### 1. 获取当前用户信息
- **接口地址**: `GET /api/user/profile`
- **请求头**: `Authorization: Bearer {token}`
- **成功响应**:
```json
{
    "code": 200,
    "message": "获取用户信息成功",
    "data": {
        "userId": 1,
        "userAccount": "test001",
        "username": "测试用户",
        "roleType": 1,
        "roleName": "监督人",
        "avatarUrl": "/uploads/avatars/avatar.jpg",
        "bio": "个人简介",
        "createTime": "2024-01-01 10:00:00"
    }
}
```

### 2. 更新用户头像
- **接口地址**: `POST /api/user/avatar`
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**: `multipart/form-data`
  - `avatar`: 头像文件（支持jpg、jpeg、png、gif格式，最大5MB）
- **成功响应**:
```json
{
    "code": 200,
    "message": "头像更新成功",
    "data": {
        "avatarUrl": "/uploads/avatars/avatar_1_uuid.jpg"
    }
}
```

### 3. 更新用户基本信息
- **接口地址**: `PUT /api/user/profile`
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:
```json
{
    "username": "新的用户名（最大50字符）",
    "bio": "新的个人简介（最大200字符）"
}
```
- **成功响应**:
```json
{
    "code": 200,
    "message": "个人信息更新成功",
    "data": {
        "userId": 1,
        "userAccount": "test001",
        "username": "新的用户名",
        "roleType": 1,
        "roleName": "监督人",
        "avatarUrl": "/uploads/avatars/avatar.jpg",
        "bio": "新的个人简介",
        "createTime": "2024-01-01 10:00:00"
    }
}
```

## 错误响应格式

所有接口的错误响应格式统一为：
```json
{
    "code": 错误码,
    "message": "错误信息",
    "data": null
}
```

### 常见错误码
- `400`: 请求参数错误
- `401`: 未授权（未登录或令牌无效）
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

## 惩罚类型管理接口

### 1. 获取惩罚类型列表（监督人和被监督人都可访问）
- **接口地址**: `GET /api/shared/punishment-categories`
- **请求头**: `Authorization: Bearer {token}`
- **成功响应**:
```json
{
    "code": 200,
    "message": "获取惩罚类型列表成功",
    "data": [
        {
            "categoryId": 1,
            "supervisorId": 1,
            "categoryName": "运动",
            "categoryDesc": "体育锻炼相关的惩罚",
            "createTime": "2024-01-01 10:00:00"
        },
        {
            "categoryId": 2,
            "supervisorId": 1,
            "categoryName": "学习",
            "categoryDesc": "学习任务相关的惩罚",
            "createTime": "2024-01-01 11:00:00"
        }
    ]
}
```

### 2. 获取惩罚类型详情（监督人和被监督人都可访问）
- **接口地址**: `GET /api/shared/punishment-categories/{categoryId}`
- **请求头**: `Authorization: Bearer {token}`
- **成功响应**:
```json
{
    "code": 200,
    "message": "获取惩罚类型详情成功",
    "data": {
        "categoryId": 1,
        "supervisorId": 1,
        "categoryName": "运动",
        "categoryDesc": "体育锻炼相关的惩罚",
        "createTime": "2024-01-01 10:00:00"
    }
}
```

### 3. 新增惩罚类型（仅监督人可访问）
- **接口地址**: `POST /api/supervisor/punishment-categories`
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:
```json
{
    "categoryName": "运动（最大50字符）",
    "categoryDesc": "类型描述（最大200字符，可为空）"
}
```
- **成功响应**:
```json
{
    "code": 200,
    "message": "惩罚类型创建成功",
    "data": {
        "categoryId": 3,
        "supervisorId": 1,
        "categoryName": "运动",
        "categoryDesc": "体育锻炼相关的惩罚",
        "createTime": "2024-01-01 12:00:00"
    }
}
```

### 4. 编辑惩罚类型（仅监督人可访问）
- **接口地址**: `PUT /api/supervisor/punishment-categories/{categoryId}`
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:
```json
{
    "categoryName": "新的类型名称（最大50字符）",
    "categoryDesc": "新的类型描述（最大200字符，可为空）"
}
```
- **成功响应**:
```json
{
    "code": 200,
    "message": "惩罚类型更新成功",
    "data": {
        "categoryId": 1,
        "supervisorId": 1,
        "categoryName": "新的类型名称",
        "categoryDesc": "新的类型描述",
        "createTime": "2024-01-01 10:00:00"
    }
}
```

### 5. 删除惩罚类型（仅监督人可访问）
- **接口地址**: `DELETE /api/supervisor/punishment-categories/{categoryId}`
- **请求头**: `Authorization: Bearer {token}`
- **成功响应**:
```json
{
    "code": 200,
    "message": "惩罚类型删除成功",
    "data": null
}
```

## 注意事项

1. **认证方式**: 除了登录接口外，其他接口都需要在请求头中携带JWT令牌
2. **令牌格式**: `Authorization: Bearer {token}`
3. **文件上传**: 头像上传支持jpg、jpeg、png、gif格式，文件大小限制为5MB
4. **字符限制**:
   - 用户名最大50个字符
   - 个人简介最大200个字符
   - 惩罚类型名称最大50个字符
   - 惩罚类型描述最大200个字符
5. **角色类型**:
   - 1 = 监督人
   - 2 = 被监督人
6. **惩罚类型权限**:
   - 监督人：可以新增、编辑、删除、查看自己创建的惩罚类型
   - 被监督人：只能查看相关监督人创建的惩罚类型
7. **删除限制**: 正在被使用的惩罚类型无法删除（被事项、计划、记录或设为默认惩罚类型）
