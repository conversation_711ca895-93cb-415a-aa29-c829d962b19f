package com.example.supervise.mapper;

import com.example.supervise.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 用户数据访问接口
 * 负责用户相关的数据库操作
 */
@Mapper
public interface UserMapper {
    
    /**
     * 根据用户账号查询用户信息
     * @param userAccount 用户账号
     * @return 用户对象，如果不存在返回null
     */
    User findByUserAccount(@Param("userAccount") String userAccount);
    
    /**
     * 根据用户ID查询用户信息
     * @param userId 用户ID
     * @return 用户对象，如果不存在返回null
     */
    User findByUserId(@Param("userId") Long userId);
    
    /**
     * 更新用户头像
     * @param userId 用户ID
     * @param avatarUrl 新的头像URL
     * @return 影响的行数
     */
    int updateAvatar(@Param("userId") Long userId, @Param("avatarUrl") String avatarUrl);
    
    /**
     * 更新用户基本信息（用户名和个人简介）
     * @param userId 用户ID
     * @param username 新的用户名
     * @param bio 新的个人简介
     * @return 影响的行数
     */
    int updateProfile(@Param("userId") Long userId, 
                     @Param("username") String username, 
                     @Param("bio") String bio);
    
    /**
     * 检查用户账号是否存在（排除指定用户ID）
     * @param userAccount 用户账号
     * @param excludeUserId 要排除的用户ID
     * @return 存在的用户数量
     */
    int countByUserAccountExcludeUserId(@Param("userAccount") String userAccount, 
                                       @Param("excludeUserId") Long excludeUserId);
}
