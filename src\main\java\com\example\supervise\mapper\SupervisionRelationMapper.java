package com.example.supervise.mapper;

import com.example.supervise.entity.SupervisionRelation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 监督关系数据访问接口
 * 负责监督关系相关的数据库操作
 */
@Mapper
public interface SupervisionRelationMapper {

    /**
     * 根据被监督人ID查询监督关系
     * @param superviseeId 被监督人ID
     * @return 监督关系对象，如果不存在返回null
     */
    SupervisionRelation findBySuperviseeId(@Param("superviseeId") Long superviseeId);

    /**
     * 根据监督人ID查询所有监督关系
     * @param supervisorId 监督人ID
     * @return 监督关系列表
     */
    List<SupervisionRelation> findBySupervisorId(@Param("supervisorId") Long supervisorId);

    /**
     * 根据监督人ID和被监督人ID查询监督关系
     * @param supervisorId 监督人ID
     * @param superviseeId 被监督人ID
     * @return 监督关系对象，如果不存在返回null
     */
    SupervisionRelation findBySupervisorIdAndSuperviseeId(@Param("supervisorId") Long supervisorId, 
                                                          @Param("superviseeId") Long superviseeId);

    /**
     * 根据关系ID查询监督关系
     * @param relationId 关系ID
     * @return 监督关系对象，如果不存在返回null
     */
    SupervisionRelation findByRelationId(@Param("relationId") Long relationId);

    /**
     * 新增监督关系
     * @param supervisionRelation 监督关系对象
     * @return 影响的行数
     */
    int insert(SupervisionRelation supervisionRelation);

    /**
     * 更新监督关系的默认惩罚设置
     * @param relationId 关系ID
     * @param defaultPunishmentCategory 默认惩罚类型ID
     * @param defaultPunishmentCount 默认惩罚数量
     * @return 影响的行数
     */
    int updateDefaultPunishment(@Param("relationId") Long relationId,
                               @Param("defaultPunishmentCategory") Long defaultPunishmentCategory,
                               @Param("defaultPunishmentCount") Integer defaultPunishmentCount);

    /**
     * 删除监督关系
     * @param relationId 关系ID
     * @param supervisorId 监督人ID（用于权限验证）
     * @return 影响的行数
     */
    int delete(@Param("relationId") Long relationId, @Param("supervisorId") Long supervisorId);

    /**
     * 检查监督关系是否存在
     * @param supervisorId 监督人ID
     * @param superviseeId 被监督人ID
     * @return 存在的关系数量
     */
    int countBySupervisorIdAndSuperviseeId(@Param("supervisorId") Long supervisorId, 
                                          @Param("superviseeId") Long superviseeId);
}
