package com.example.supervise.mapper;

import com.example.supervise.entity.PunishmentRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 惩罚记录数据访问接口
 * 负责惩罚记录相关的数据库操作
 */
@Mapper
public interface PunishmentRecordMapper {

    /**
     * 根据监督关系ID查询所有惩罚记录
     * @param relationId 监督关系ID
     * @return 惩罚记录列表
     */
    List<PunishmentRecord> findByRelationId(@Param("relationId") Long relationId);

    /**
     * 根据监督关系ID查询惩罚记录（带分页）
     * @param relationId 监督关系ID
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 惩罚记录列表
     */
    List<PunishmentRecord> findByRelationIdWithPaging(@Param("relationId") Long relationId,
                                                      @Param("offset") Integer offset,
                                                      @Param("limit") Integer limit);

    /**
     * 根据监督关系ID统计惩罚记录总数
     * @param relationId 监督关系ID
     * @return 记录总数
     */
    int countByRelationId(@Param("relationId") Long relationId);

    /**
     * 根据记录ID查询惩罚记录
     * @param recordId 记录ID
     * @return 惩罚记录对象，如果不存在返回null
     */
    PunishmentRecord findByRecordId(@Param("recordId") Long recordId);

    /**
     * 根据来源查询惩罚记录
     * @param relationId 监督关系ID
     * @param sourceType 来源类型
     * @param sourceId 来源ID
     * @return 惩罚记录列表
     */
    List<PunishmentRecord> findBySource(@Param("relationId") Long relationId,
                                       @Param("sourceType") Integer sourceType,
                                       @Param("sourceId") Long sourceId);

    /**
     * 根据惩罚类型查询记录
     * @param relationId 监督关系ID
     * @param categoryId 惩罚类型ID
     * @return 惩罚记录列表
     */
    List<PunishmentRecord> findByCategoryId(@Param("relationId") Long relationId,
                                           @Param("categoryId") Long categoryId);

    /**
     * 新增惩罚记录
     * @param punishmentRecord 惩罚记录对象
     * @return 影响的行数
     */
    int insert(PunishmentRecord punishmentRecord);

    /**
     * 批量新增惩罚记录
     * @param records 惩罚记录列表
     * @return 影响的行数
     */
    int batchInsert(@Param("records") List<PunishmentRecord> records);

    /**
     * 删除惩罚记录
     * @param recordId 记录ID
     * @param operatorId 操作人ID（用于权限验证）
     * @return 影响的行数
     */
    int delete(@Param("recordId") Long recordId, @Param("operatorId") Long operatorId);

    /**
     * 根据来源删除惩罚记录
     * @param relationId 监督关系ID
     * @param sourceType 来源类型
     * @param sourceId 来源ID
     * @return 影响的行数
     */
    int deleteBySource(@Param("relationId") Long relationId,
                      @Param("sourceType") Integer sourceType,
                      @Param("sourceId") Long sourceId);

    /**
     * 获取惩罚记录统计信息（按类型分组）
     * @param relationId 监督关系ID
     * @return 统计信息列表，包含categoryId, categoryName, totalCount等字段
     */
    List<Map<String, Object>> getStatisticsByCategory(@Param("relationId") Long relationId);

    /**
     * 获取惩罚记录统计信息（按来源类型分组）
     * @param relationId 监督关系ID
     * @return 统计信息列表，包含sourceType, totalCount等字段
     */
    List<Map<String, Object>> getStatisticsBySourceType(@Param("relationId") Long relationId);

    /**
     * 获取当前惩罚数量（按类型统计）
     * @param relationId 监督关系ID
     * @return 当前惩罚数量统计，包含categoryId, categoryName, currentCount等字段
     */
    List<Map<String, Object>> getCurrentPunishmentCounts(@Param("relationId") Long relationId);
}
