package com.example.supervise.filter;

import com.example.supervise.util.JwtUtil;
import com.example.supervise.util.Response;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.Collections;

/**
 * JWT认证过滤器
 * 用于验证请求中的JWT令牌，并设置用户认证信息
 */
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    private final JwtUtil jwtUtil;

    /**
     * 构造函数，注入JwtUtil工具类
     */
    public JwtAuthenticationFilter(JwtUtil jwtUtil) {
        this.jwtUtil = jwtUtil;
    }

    /**
     * 过滤器核心方法，处理每个HTTP请求
     */
    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, 
                                    FilterChain filterChain) throws ServletException, IOException {
        
        // 获取请求路径，用于判断是否需要验证
        String requestPath = request.getRequestURI();
        
        // 对于公开接口，直接放行，不需要JWT验证
        if (isPublicPath(requestPath)) {
            filterChain.doFilter(request, response);
            return;
        }

        // 从请求头中获取Authorization字段
        String authHeader = request.getHeader("Authorization");
        
        // 检查Authorization头是否存在且以"Bearer "开头
        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            // 如果没有有效的Authorization头，返回401未授权错误
            handleAuthenticationError(response, "缺少有效的认证令牌");
            return;
        }

        // 提取JWT令牌（去掉"Bearer "前缀）
        String token = authHeader.substring(7);
        
        // 验证JWT令牌的有效性
        int validationResult = jwtUtil.validateToken(token);
        
        if (validationResult != 0) {
            // 令牌验证失败，根据错误码返回相应的错误信息
            String errorMessage = getErrorMessage(validationResult);
            handleAuthenticationError(response, errorMessage);
            return;
        }

        // 令牌验证成功，从令牌中提取用户信息
        try {
            Long userId = jwtUtil.getUserIdFromToken(token);
            String userAccount = jwtUtil.getUserAccountFromToken(token);
            String role = jwtUtil.getRoleFromToken(token);
            
            // 检查必要信息是否完整
            if (userId == null || userAccount == null || role == null) {
                handleAuthenticationError(response, "令牌信息不完整");
                return;
            }

            // 根据角色创建Spring Security的权限
            String authority = "ROLE_" + role.toUpperCase();
            SimpleGrantedAuthority grantedAuthority = new SimpleGrantedAuthority(authority);
            
            // 创建认证对象，包含用户ID、用户账号和权限信息
            UsernamePasswordAuthenticationToken authentication = 
                new UsernamePasswordAuthenticationToken(
                    userAccount, // principal：用户账号
                    null,        // credentials：密码（JWT认证不需要）
                    Collections.singletonList(grantedAuthority) // authorities：用户权限
                );
            
            // 在认证对象中添加额外的用户信息
            authentication.setDetails(userId);
            
            // 将认证信息设置到Spring Security上下文中
            SecurityContextHolder.getContext().setAuthentication(authentication);
            
        } catch (Exception e) {
            // 处理令牌解析过程中的异常
            handleAuthenticationError(response, "令牌解析失败");
            return;
        }

        // 继续执行过滤器链
        filterChain.doFilter(request, response);
    }

    /**
     * 判断请求路径是否为公开路径（不需要认证）
     * @param path 请求路径
     * @return true=公开路径，false=需要认证的路径
     */
    private boolean isPublicPath(String path) {
        // 定义不需要JWT验证的公开路径
        String[] publicPaths = {
            "/api/public/",     // 公开API接口
            "/api/auth/",       // 认证相关接口（登录、注册等）
            "/uploads/",        // 静态文件上传路径
            "/static/",         // 静态资源路径
            "/error",           // 错误页面
            "/favicon.ico"      // 网站图标
        };
        
        // 检查当前路径是否匹配任何公开路径
        for (String publicPath : publicPaths) {
            if (path.startsWith(publicPath)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 根据验证结果码获取错误信息
     * @param validationResult 验证结果码
     * @return 错误信息字符串
     */
    private String getErrorMessage(int validationResult) {
        switch (validationResult) {
            case 1001:
                return "令牌为空";
            case 1002:
                return "令牌无效或已过期";
            case 1003:
                return "令牌已失效";
            default:
                return "认证失败";
        }
    }

    /**
     * 处理认证错误，返回JSON格式的错误响应
     * @param response HTTP响应对象
     * @param message 错误信息
     */
    private void handleAuthenticationError(HttpServletResponse response, String message) throws IOException {
        // 设置响应状态码为401（未授权）
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        // 设置响应内容类型为JSON，编码为UTF-8
        response.setContentType("application/json;charset=UTF-8");
        
        // 使用Response工具类生成标准格式的JSON错误响应
        String errorResponse = Response.toJSON(401, "认证失败：" + message, null);
        
        // 将错误响应写入HTTP响应体
        response.getWriter().write(errorResponse);
    }
}
