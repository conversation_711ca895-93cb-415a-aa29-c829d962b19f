package com.example.supervise.service;

import com.example.supervise.dto.LoginRequest;
import com.example.supervise.dto.UpdateProfileRequest;
import com.example.supervise.entity.User;
import com.example.supervise.mapper.UserMapper;
import com.example.supervise.util.JwtUtil;
import com.example.supervise.util.MD5Util;
import com.example.supervise.util.Response;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 用户服务类
 * 处理用户相关的业务逻辑
 */
@Service
public class UserService {

    private final UserMapper userMapper;
    private final JwtUtil jwtUtil;

    /**
     * 构造函数，注入依赖
     */
    public UserService(UserMapper userMapper, JwtUtil jwtUtil) {
        this.userMapper = userMapper;
        this.jwtUtil = jwtUtil;
    }

    /**
     * 用户登录
     * @param loginRequest 登录请求信息
     * @return 登录结果JSON字符串
     */
    public String login(LoginRequest loginRequest) {
        // 参数验证
        if (loginRequest.getUserAccount() == null || loginRequest.getUserAccount().trim().isEmpty()) {
            return Response.toJSON(400, "用户账号不能为空", null);
        }
        
        if (loginRequest.getPassword() == null || loginRequest.getPassword().trim().isEmpty()) {
            return Response.toJSON(400, "密码不能为空", null);
        }

        try {
            // 根据账号查询用户
            User user = userMapper.findByUserAccount(loginRequest.getUserAccount().trim());
            if (user == null) {
                return Response.toJSON(401, "用户账号或密码错误", null);
            }

            // 验证密码（使用MD5加密）
            String inputPasswordHash = MD5Util.encrypt(loginRequest.getPassword());
            if (!inputPasswordHash.equals(user.getPasswordHash())) {
                return Response.toJSON(401, "用户账号或密码错误", null);
            }

            // 生成JWT令牌
            String token = jwtUtil.generateUserToken(user);

            // 构建返回数据
            Map<String, Object> loginData = new HashMap<>();
            loginData.put("token", token);
            loginData.put("userInfo", buildUserInfo(user));

            return Response.toJSON(200, "登录成功", loginData);

        } catch (Exception e) {
            return Response.toJSON(500, "登录失败：" + e.getMessage(), null);
        }
    }

    /**
     * 更新用户头像
     * @param userId 用户ID
     * @param avatarFile 头像文件
     * @return 更新结果JSON字符串
     */
    public String updateAvatar(Long userId, MultipartFile avatarFile) {
        // 参数验证
        if (avatarFile == null || avatarFile.isEmpty()) {
            return Response.toJSON(400, "请选择要上传的头像文件", null);
        }

        // 验证文件类型
        String originalFilename = avatarFile.getOriginalFilename();
        if (originalFilename == null || !isValidImageFile(originalFilename)) {
            return Response.toJSON(400, "只支持jpg、jpeg、png、gif格式的图片文件", null);
        }

        // 验证文件大小（限制为5MB）
        if (avatarFile.getSize() > 5 * 1024 * 1024) {
            return Response.toJSON(400, "头像文件大小不能超过5MB", null);
        }

        try {
            // 验证用户是否存在
            User user = userMapper.findByUserId(userId);
            if (user == null) {
                return Response.toJSON(404, "用户不存在", null);
            }

            // 生成唯一文件名
            String fileExtension = getFileExtension(originalFilename);
            String newFileName = "avatar_" + userId + "_" + UUID.randomUUID().toString() + "." + fileExtension;
            
            // 创建上传目录
            String uploadDir = System.getProperty("user.dir") + "/uploads/avatars/";
            File uploadDirFile = new File(uploadDir);
            if (!uploadDirFile.exists()) {
                uploadDirFile.mkdirs();
            }

            // 保存文件
            File destFile = new File(uploadDir + newFileName);
            avatarFile.transferTo(destFile);

            // 构建头像URL
            String avatarUrl = "/uploads/avatars/" + newFileName;

            // 更新数据库
            int updateResult = userMapper.updateAvatar(userId, avatarUrl);
            if (updateResult > 0) {
                // 删除旧头像文件（如果存在且不是默认头像）
                deleteOldAvatar(user.getAvatarUrl());

                Map<String, Object> resultData = new HashMap<>();
                resultData.put("avatarUrl", avatarUrl);
                return Response.toJSON(200, "头像更新成功", resultData);
            } else {
                // 删除刚上传的文件
                destFile.delete();
                return Response.toJSON(500, "头像更新失败", null);
            }

        } catch (IOException e) {
            return Response.toJSON(500, "文件上传失败：" + e.getMessage(), null);
        } catch (Exception e) {
            return Response.toJSON(500, "头像更新失败：" + e.getMessage(), null);
        }
    }

    /**
     * 更新用户基本信息
     * @param userId 用户ID
     * @param updateRequest 更新请求信息
     * @return 更新结果JSON字符串
     */
    public String updateProfile(Long userId, UpdateProfileRequest updateRequest) {
        // 参数验证
        if (updateRequest.getUsername() == null || updateRequest.getUsername().trim().isEmpty()) {
            return Response.toJSON(400, "用户名不能为空", null);
        }

        if (updateRequest.getUsername().trim().length() > 50) {
            return Response.toJSON(400, "用户名长度不能超过50个字符", null);
        }

        if (!updateRequest.isValidBio()) {
            return Response.toJSON(400, "个人简介不能超过200个字符", null);
        }

        try {
            // 验证用户是否存在
            User user = userMapper.findByUserId(userId);
            if (user == null) {
                return Response.toJSON(404, "用户不存在", null);
            }

            // 更新数据库
            int updateResult = userMapper.updateProfile(
                userId, 
                updateRequest.getUsername().trim(), 
                updateRequest.getBio() != null ? updateRequest.getBio().trim() : null
            );

            if (updateResult > 0) {
                // 获取更新后的用户信息
                User updatedUser = userMapper.findByUserId(userId);
                return Response.toJSON(200, "个人信息更新成功", buildUserInfo(updatedUser));
            } else {
                return Response.toJSON(500, "个人信息更新失败", null);
            }

        } catch (Exception e) {
            return Response.toJSON(500, "个人信息更新失败：" + e.getMessage(), null);
        }
    }

    /**
     * 获取用户信息
     * @param userId 用户ID
     * @return 用户信息JSON字符串
     */
    public String getUserInfo(Long userId) {
        try {
            User user = userMapper.findByUserId(userId);
            if (user == null) {
                return Response.toJSON(404, "用户不存在", null);
            }

            return Response.toJSON(200, "获取用户信息成功", buildUserInfo(user));

        } catch (Exception e) {
            return Response.toJSON(500, "获取用户信息失败：" + e.getMessage(), null);
        }
    }

    /**
     * 构建用户信息对象（不包含敏感信息）
     * @param user 用户对象
     * @return 用户信息Map
     */
    private Map<String, Object> buildUserInfo(User user) {
        Map<String, Object> userInfo = new HashMap<>();
        userInfo.put("userId", user.getUserId());
        userInfo.put("userAccount", user.getUserAccount());
        userInfo.put("username", user.getUsername());
        userInfo.put("roleType", user.getRoleType());
        userInfo.put("roleName", user.getRoleName());
        userInfo.put("avatarUrl", user.getAvatarUrl());
        userInfo.put("bio", user.getBio());
        userInfo.put("createTime", user.getCreateTime());
        return userInfo;
    }

    /**
     * 验证是否为有效的图片文件
     * @param filename 文件名
     * @return true=有效图片文件，false=无效文件
     */
    private boolean isValidImageFile(String filename) {
        String extension = getFileExtension(filename).toLowerCase();
        return "jpg".equals(extension) || "jpeg".equals(extension) || 
               "png".equals(extension) || "gif".equals(extension);
    }

    /**
     * 获取文件扩展名
     * @param filename 文件名
     * @return 文件扩展名
     */
    private String getFileExtension(String filename) {
        int lastDotIndex = filename.lastIndexOf('.');
        return lastDotIndex > 0 ? filename.substring(lastDotIndex + 1) : "";
    }

    /**
     * 删除旧头像文件
     * @param oldAvatarUrl 旧头像URL
     */
    private void deleteOldAvatar(String oldAvatarUrl) {
        if (oldAvatarUrl != null && oldAvatarUrl.startsWith("/uploads/avatars/")) {
            try {
                String filePath = System.getProperty("user.dir") + oldAvatarUrl;
                File oldFile = new File(filePath);
                if (oldFile.exists()) {
                    oldFile.delete();
                }
            } catch (Exception e) {
                // 忽略删除失败的情况
            }
        }
    }
}
