# ===== 应用基本配置 =====
spring.application.name=supervise
server.port=8080

# ===== 数据库配置 =====
spring.datasource.url=*************************************
spring.datasource.username=root
spring.datasource.password=root123456
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# ===== MyBatis配置 =====
mybatis.mapper-locations=classpath:mapper/*.xml
mybatis.type-aliases-package=com.example.supervise.entity
mybatis.configuration.map-underscore-to-camel-case=true

# ===== JSON序列化配置 =====
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.time-zone=GMT+8

# ===== 文件上传配置 =====
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=20MB

# ===== 静态资源配置 =====
spring.mvc.static-path-pattern=/static/**,/uploads/**
spring.web.resources.static-locations=classpath:/static/,file:${user.dir}/uploads/

# ===== CORS跨域配置 =====
# 允许的前端域名，多个域名用逗号分隔
cors.allowed-origins=http://localhost:5173,http://1********:5173,http://localhost:3000,http://1********:3000

# ===== 安全配置 =====
# JWT令牌有效期（小时）
jwt.expiration-hours=24

# ===== 日志配置 =====
# 设置Spring Security的日志级别（可选，用于调试）
logging.level.org.springframework.security=DEBUG