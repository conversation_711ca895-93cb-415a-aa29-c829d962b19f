spring.application.name=supervise
server.port=8080

spring.datasource.url=*************************************
spring.datasource.username=root
spring.datasource.password=root123456
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver


mybatis.mapper-locations=classpath:mapper/*.xml
mybatis.type-aliases-package=com.example.supervise.entity
mybatis.configuration.map-underscore-to-camel-case=true


spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.time-zone=GMT+8


spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=20MB


spring.mvc.static-path-pattern=/static/**,/uploads/**
spring.web.resources.static-locations=classpath:/static/,file:${user.dir}/uploads/


cors.allowed-origins=http://localhost:5173,http://127.0.0.1:5173,http://localhost:3000,http://127.0.0.1:3000


jwt.expiration-hours=24

logging.level.org.springframework.security=DEBUG