package com.example.supervise.controller;

import com.example.supervise.dto.PunishmentCategoryRequest;
import com.example.supervise.service.PunishmentCategoryService;
import com.example.supervise.util.Response;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

/**
 * 惩罚类型控制器
 * 处理惩罚类型相关的接口请求
 */
@RestController
@RequestMapping("/api")
public class PunishmentCategoryController {

    private final PunishmentCategoryService punishmentCategoryService;

    /**
     * 构造函数，注入依赖服务
     */
    public PunishmentCategoryController(PunishmentCategoryService punishmentCategoryService) {
        this.punishmentCategoryService = punishmentCategoryService;
    }

    /**
     * 获取惩罚类型列表接口（监督人和被监督人都可以访问）
     * @return 惩罚类型列表JSON字符串
     */
    @GetMapping("/shared/punishment-categories")
    public String getPunishmentCategories() {
        try {
            // 获取当前用户信息
            Long currentUserId = getCurrentUserId();
            String currentUserRole = getCurrentUserRole();

            if (currentUserId == null || currentUserRole == null) {
                return Response.toJSON(401, "用户未登录", null);
            }

            // 调用服务层方法，根据用户角色获取惩罚类型
            return punishmentCategoryService.getPunishmentCategories(currentUserId, currentUserRole);

        } catch (Exception e) {
            return Response.toJSON(500, "获取惩罚类型列表失败：" + e.getMessage(), null);
        }
    }

    /**
     * 获取惩罚类型详情接口（监督人和被监督人都可以访问）
     * @param categoryId 类型ID
     * @return 惩罚类型详情JSON字符串
     */
    @GetMapping("/shared/punishment-categories/{categoryId}")
    public String getPunishmentCategoryById(@PathVariable Long categoryId) {
        try {
            Long currentUserId = getCurrentUserId();
            if (currentUserId == null) {
                return Response.toJSON(401, "用户未登录", null);
            }

            return punishmentCategoryService.getPunishmentCategoryById(categoryId, currentUserId);

        } catch (Exception e) {
            return Response.toJSON(500, "获取惩罚类型详情失败：" + e.getMessage(), null);
        }
    }

    /**
     * 新增惩罚类型接口（仅监督人可以访问）
     * @param request 惩罚类型请求信息
     * @return 新增结果JSON字符串
     */
    @PostMapping("/supervisor/punishment-categories")
    public String createPunishmentCategory(@RequestBody PunishmentCategoryRequest request) {
        try {
            Long supervisorId = getCurrentUserId();
            if (supervisorId == null) {
                return Response.toJSON(401, "用户未登录", null);
            }

            return punishmentCategoryService.createPunishmentCategory(supervisorId, request);

        } catch (Exception e) {
            return Response.toJSON(500, "创建惩罚类型失败：" + e.getMessage(), null);
        }
    }

    /**
     * 编辑惩罚类型接口（仅监督人可以访问）
     * @param categoryId 类型ID
     * @param request 惩罚类型请求信息
     * @return 编辑结果JSON字符串
     */
    @PutMapping("/supervisor/punishment-categories/{categoryId}")
    public String updatePunishmentCategory(@PathVariable Long categoryId,
                                          @RequestBody PunishmentCategoryRequest request) {
        try {
            Long supervisorId = getCurrentUserId();
            if (supervisorId == null) {
                return Response.toJSON(401, "用户未登录", null);
            }

            return punishmentCategoryService.updatePunishmentCategory(categoryId, supervisorId, request);

        } catch (Exception e) {
            return Response.toJSON(500, "更新惩罚类型失败：" + e.getMessage(), null);
        }
    }

    /**
     * 删除惩罚类型接口（仅监督人可以访问）
     * @param categoryId 类型ID
     * @return 删除结果JSON字符串
     */
    @DeleteMapping("/supervisor/punishment-categories/{categoryId}")
    public String deletePunishmentCategory(@PathVariable Long categoryId) {
        try {
            Long supervisorId = getCurrentUserId();
            if (supervisorId == null) {
                return Response.toJSON(401, "用户未登录", null);
            }

            return punishmentCategoryService.deletePunishmentCategory(categoryId, supervisorId);

        } catch (Exception e) {
            return Response.toJSON(500, "删除惩罚类型失败：" + e.getMessage(), null);
        }
    }

    /**
     * 从Spring Security上下文中获取当前用户ID
     * @return 用户ID，如果获取失败返回null
     */
    private Long getCurrentUserId() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.getDetails() instanceof Long) {
                return (Long) authentication.getDetails();
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 从Spring Security上下文中获取当前用户角色
     * @return 用户角色，如果获取失败返回null
     */
    private String getCurrentUserRole() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.getAuthorities() != null && !authentication.getAuthorities().isEmpty()) {
                String authority = authentication.getAuthorities().iterator().next().getAuthority();
                // 去掉"ROLE_"前缀，返回小写的角色名
                if (authority.startsWith("ROLE_")) {
                    return authority.substring(5).toLowerCase();
                }
                return authority.toLowerCase();
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }
}
