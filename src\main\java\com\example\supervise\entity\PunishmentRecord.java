package com.example.supervise.entity;

import java.time.LocalDateTime;

/**
 * 惩罚记录实体类
 * 对应数据库表：punishment_records
 */
public class PunishmentRecord {

    /**
     * 惩罚记录主键ID
     */
    private Long recordId;

    /**
     * 监督关系ID（外键）
     */
    private Long relationId;

    /**
     * 来源类型（1=事项，2=计划，3=日记，4=手动）
     */
    private Integer sourceType;

    /**
     * 来源ID（事项ID/计划ID/日记ID）
     */
    private Long sourceId;

    /**
     * 惩罚类型ID（外键）
     */
    private Long categoryId;

    /**
     * 数量变化类型（1=增加，2=减少，3=清零）
     */
    private Integer changeType;

    /**
     * 变化数量（正数增加，负数减少）
     */
    private Integer changeCount;

    /**
     * 操作人ID（监督人或系统）
     */
    private Long operatorId;

    /**
     * 操作时间
     */
    private LocalDateTime operateTime;

    /**
     * 操作原因
     */
    private String reason;

    // 来源类型常量
    public static final int SOURCE_TYPE_MATTER = 1;    // 事项
    public static final int SOURCE_TYPE_PLAN = 2;      // 计划
    public static final int SOURCE_TYPE_DIARY = 3;     // 日记
    public static final int SOURCE_TYPE_MANUAL = 4;    // 手动

    // 变化类型常量
    public static final int CHANGE_TYPE_INCREASE = 1;  // 增加
    public static final int CHANGE_TYPE_DECREASE = 2;  // 减少
    public static final int CHANGE_TYPE_CLEAR = 3;     // 清零

    /**
     * 无参构造函数
     */
    public PunishmentRecord() {
    }

    /**
     * 全参构造函数
     */
    public PunishmentRecord(Long recordId, Long relationId, Integer sourceType, Long sourceId,
                           Long categoryId, Integer changeType, Integer changeCount, Long operatorId,
                           LocalDateTime operateTime, String reason) {
        this.recordId = recordId;
        this.relationId = relationId;
        this.sourceType = sourceType;
        this.sourceId = sourceId;
        this.categoryId = categoryId;
        this.changeType = changeType;
        this.changeCount = changeCount;
        this.operatorId = operatorId;
        this.operateTime = operateTime;
        this.reason = reason;
    }

    /**
     * 获取惩罚记录主键ID
     */
    public Long getRecordId() {
        return recordId;
    }

    /**
     * 设置惩罚记录主键ID
     */
    public void setRecordId(Long recordId) {
        this.recordId = recordId;
    }

    /**
     * 获取监督关系ID
     */
    public Long getRelationId() {
        return relationId;
    }

    /**
     * 设置监督关系ID
     */
    public void setRelationId(Long relationId) {
        this.relationId = relationId;
    }

    /**
     * 获取来源类型
     */
    public Integer getSourceType() {
        return sourceType;
    }

    /**
     * 设置来源类型
     */
    public void setSourceType(Integer sourceType) {
        this.sourceType = sourceType;
    }

    /**
     * 获取来源ID
     */
    public Long getSourceId() {
        return sourceId;
    }

    /**
     * 设置来源ID
     */
    public void setSourceId(Long sourceId) {
        this.sourceId = sourceId;
    }

    /**
     * 获取惩罚类型ID
     */
    public Long getCategoryId() {
        return categoryId;
    }

    /**
     * 设置惩罚类型ID
     */
    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    /**
     * 获取数量变化类型
     */
    public Integer getChangeType() {
        return changeType;
    }

    /**
     * 设置数量变化类型
     */
    public void setChangeType(Integer changeType) {
        this.changeType = changeType;
    }

    /**
     * 获取变化数量
     */
    public Integer getChangeCount() {
        return changeCount;
    }

    /**
     * 设置变化数量
     */
    public void setChangeCount(Integer changeCount) {
        this.changeCount = changeCount;
    }

    /**
     * 获取操作人ID
     */
    public Long getOperatorId() {
        return operatorId;
    }

    /**
     * 设置操作人ID
     */
    public void setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
    }

    /**
     * 获取操作时间
     */
    public LocalDateTime getOperateTime() {
        return operateTime;
    }

    /**
     * 设置操作时间
     */
    public void setOperateTime(LocalDateTime operateTime) {
        this.operateTime = operateTime;
    }

    /**
     * 获取操作原因
     */
    public String getReason() {
        return reason;
    }

    /**
     * 设置操作原因
     */
    public void setReason(String reason) {
        this.reason = reason;
    }

    /**
     * 获取来源类型名称
     */
    public String getSourceTypeName() {
        if (sourceType == null) {
            return "未知";
        }
        switch (sourceType) {
            case SOURCE_TYPE_MATTER:
                return "事项";
            case SOURCE_TYPE_PLAN:
                return "计划";
            case SOURCE_TYPE_DIARY:
                return "日记";
            case SOURCE_TYPE_MANUAL:
                return "手动";
            default:
                return "未知";
        }
    }

    /**
     * 获取变化类型名称
     */
    public String getChangeTypeName() {
        if (changeType == null) {
            return "未知";
        }
        switch (changeType) {
            case CHANGE_TYPE_INCREASE:
                return "增加";
            case CHANGE_TYPE_DECREASE:
                return "减少";
            case CHANGE_TYPE_CLEAR:
                return "清零";
            default:
                return "未知";
        }
    }

    /**
     * toString方法
     */
    @Override
    public String toString() {
        return "PunishmentRecord{" +
                "recordId=" + recordId +
                ", relationId=" + relationId +
                ", sourceType=" + sourceType +
                ", sourceId=" + sourceId +
                ", categoryId=" + categoryId +
                ", changeType=" + changeType +
                ", changeCount=" + changeCount +
                ", operatorId=" + operatorId +
                ", operateTime=" + operateTime +
                ", reason='" + reason + '\'' +
                '}';
    }

    /**
     * equals方法
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        PunishmentRecord that = (PunishmentRecord) obj;
        return recordId != null && recordId.equals(that.recordId);
    }

    /**
     * hashCode方法
     */
    @Override
    public int hashCode() {
        return recordId != null ? recordId.hashCode() : 0;
    }
}
