package com.example.supervise.mapper;

import com.example.supervise.entity.PunishmentCategory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 惩罚类型数据访问接口
 * 负责惩罚类型相关的数据库操作
 */
@Mapper
public interface PunishmentCategoryMapper {

    /**
     * 根据监督人ID查询所有惩罚类型
     * @param supervisorId 监督人ID
     * @return 惩罚类型列表
     */
    List<PunishmentCategory> findBySupervisorId(@Param("supervisorId") Long supervisorId);

    /**
     * 根据类型ID查询惩罚类型
     * @param categoryId 类型ID
     * @return 惩罚类型对象，如果不存在返回null
     */
    PunishmentCategory findByCategoryId(@Param("categoryId") Long categoryId);

    /**
     * 根据监督人ID和类型名称查询惩罚类型（用于检查重名）
     * @param supervisorId 监督人ID
     * @param categoryName 类型名称
     * @return 惩罚类型对象，如果不存在返回null
     */
    PunishmentCategory findBySupervisorIdAndCategoryName(@Param("supervisorId") Long supervisorId, 
                                                         @Param("categoryName") String categoryName);

    /**
     * 根据监督人ID和类型名称查询惩罚类型（排除指定ID，用于编辑时检查重名）
     * @param supervisorId 监督人ID
     * @param categoryName 类型名称
     * @param excludeCategoryId 要排除的类型ID
     * @return 惩罚类型对象，如果不存在返回null
     */
    PunishmentCategory findBySupervisorIdAndCategoryNameExcludeId(@Param("supervisorId") Long supervisorId,
                                                                  @Param("categoryName") String categoryName,
                                                                  @Param("excludeCategoryId") Long excludeCategoryId);

    /**
     * 新增惩罚类型
     * @param punishmentCategory 惩罚类型对象
     * @return 影响的行数
     */
    int insert(PunishmentCategory punishmentCategory);

    /**
     * 更新惩罚类型
     * @param categoryId 类型ID
     * @param categoryName 类型名称
     * @param categoryDesc 类型描述
     * @return 影响的行数
     */
    int update(@Param("categoryId") Long categoryId,
               @Param("categoryName") String categoryName,
               @Param("categoryDesc") String categoryDesc);

    /**
     * 删除惩罚类型
     * @param categoryId 类型ID
     * @param supervisorId 监督人ID（用于权限验证）
     * @return 影响的行数
     */
    int delete(@Param("categoryId") Long categoryId, @Param("supervisorId") Long supervisorId);

    /**
     * 检查惩罚类型是否被使用（在事项惩罚关联表中）
     * @param categoryId 类型ID
     * @return 使用次数
     */
    int countUsageInMatterPunishments(@Param("categoryId") Long categoryId);

    /**
     * 检查惩罚类型是否被使用（在计划惩罚关联表中）
     * @param categoryId 类型ID
     * @return 使用次数
     */
    int countUsageInPlanPunishments(@Param("categoryId") Long categoryId);

    /**
     * 检查惩罚类型是否被使用（在惩罚记录表中）
     * @param categoryId 类型ID
     * @return 使用次数
     */
    int countUsageInPunishmentRecords(@Param("categoryId") Long categoryId);

    /**
     * 检查惩罚类型是否被设置为默认惩罚类型
     * @param categoryId 类型ID
     * @return 使用次数
     */
    int countUsageAsDefaultPunishment(@Param("categoryId") Long categoryId);
}
