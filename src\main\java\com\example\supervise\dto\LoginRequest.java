package com.example.supervise.dto;

/**
 * 登录请求DTO
 * 用于接收前端传递的登录信息
 */
public class LoginRequest {
    
    /**
     * 用户账号
     */
    private String userAccount;
    
    /**
     * 密码
     */
    private String password;
    
    /**
     * 无参构造函数
     */
    public LoginRequest() {
    }
    
    /**
     * 全参构造函数
     */
    public LoginRequest(String userAccount, String password) {
        this.userAccount = userAccount;
        this.password = password;
    }
    
    /**
     * 获取用户账号
     */
    public String getUserAccount() {
        return userAccount;
    }
    
    /**
     * 设置用户账号
     */
    public void setUserAccount(String userAccount) {
        this.userAccount = userAccount;
    }
    
    /**
     * 获取密码
     */
    public String getPassword() {
        return password;
    }
    
    /**
     * 设置密码
     */
    public void setPassword(String password) {
        this.password = password;
    }
    
    /**
     * toString方法
     */
    @Override
    public String toString() {
        return "LoginRequest{" +
                "userAccount='" + userAccount + '\'' +
                ", password='[PROTECTED]'" +
                '}';
    }
}
