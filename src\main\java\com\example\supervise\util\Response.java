package com.example.supervise.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import lombok.Data;

import java.util.LinkedHashMap;
import java.util.Map;

@Data
/**
 * ResponseData（响应格式） 类用于将给定的状态码、消息和数据转换为 JSON 字符串
 */
public class Response<T> {
        /**
         * 将状态码、消息和数据转换为 JSON 字符串的静态方法
         *
         * @param code  状态码
         * @param msg   消息
         * @param data  数据
         * @return 生成的 JSON 字符串
         */
        public static String toJSON(int code, String msg, Object data) {
            // 创建一个有序的 Map 来存储响应数据
            Map<String, Object> result = new LinkedHashMap<>();
            // 向 Map 中添加状态码
            result.put("code", code);
            // 向 Map 中添加消息
            result.put("message", msg);
            // 向 Map 中添加数据
            result.put("data", data);
            // 使用 Fastjson 将 Map 转换为 JSON 字符串，并应用默认的生成特性
            return JSON.toJSONString(result, SerializerFeature.WriteDateUseDateFormat,SerializerFeature.WriteMapNullValue);
        }

}