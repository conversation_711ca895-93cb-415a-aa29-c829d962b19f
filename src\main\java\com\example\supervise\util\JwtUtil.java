package com.example.supervise.util;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.example.supervise.entity.User;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * JWT工具类
 * 用于生成和验证JWT令牌
 */
@Component
public class JwtUtil {

    // 动态生成密钥，每次启动应用时都会生成新的密钥
    private static final String SECRET_KEY = generateDynamicSecret();

    /**
     * 令牌有效期（小时），从配置文件中读取
     * 默认值为24小时，可通过jwt.expiration-hours配置项修改
     */
    @Value("${jwt.expiration-hours:24}")
    private int expirationHours;

    /**
     * 获取令牌有效期（毫秒）
     * @return 令牌有效期的毫秒数
     */
    private long getExpirationMillis() {
        return 1000L * 60 * 60 * expirationHours; // 转换为毫秒
    }

    /**
     * 获取配置的令牌有效期（小时）
     * @return 令牌有效期小时数
     */
    public int getExpirationHours() {
        return expirationHours;
    }

    // 角色类型常量
    public static final String ROLE_SUPERVISOR = "supervisor";   // 监督人
    public static final String ROLE_SUPERVISEE = "supervisee";  // 被监督人

    // 存储当前有效的令牌，键为角色+用户ID，值为令牌
    private static final ConcurrentHashMap<String, String> currentTokenMap = new ConcurrentHashMap<>();
    // 存储黑名单中的令牌，键为令牌，值为过期时间戳
    private static final ConcurrentHashMap<String, Long> blacklistTokenMap = new ConcurrentHashMap<>();

    // 定时清理任务
    private static final ScheduledExecutorService cleaner = Executors.newSingleThreadScheduledExecutor();

    // 静态初始化，启动定时任务，每小时清理一次过期数据
    static {
        cleaner.scheduleAtFixedRate(JwtUtil::cleanExpiredData, 1, 1, TimeUnit.HOURS);
    }

    /**
     * 动态生成密钥
     */
    private static String generateDynamicSecret() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 生成复合键：角色+用户ID
     */
    private static String generateKey(String role, Long userId) {
        return role + "_" + userId;
    }

    /**
     * 生成用户令牌
     * @param user 用户对象
     * @return JWT令牌字符串
     */
    public String generateUserToken(User user) {
        // 根据用户角色类型确定角色字符串
        String role = user.isSupervisor() ? ROLE_SUPERVISOR : ROLE_SUPERVISEE;

        return generateToken(
                user.getUserId(),
                user.getUserAccount(),
                user.getUsername(),
                role,
                user.getAvatarUrl(),
                user.getBio()
        );
    }

    /**
     * 通用令牌生成方法
     * @param userId 用户ID
     * @param userAccount 用户账号
     * @param username 用户名
     * @param role 角色类型
     * @param avatarUrl 头像URL
     * @param bio 个人简介
     * @return JWT令牌字符串
     */
    private String generateToken(
            Long userId,
            String userAccount,
            String username,
            String role,
            String avatarUrl,
            String bio
    ) {
        // 生成键值对，=> 角色+用户ID
        String key = generateKey(role, userId);

        // 生成令牌，并设置过期时间
        String newToken = JWT.create()
                .withClaim("userId", userId)
                .withClaim("userAccount", userAccount)
                .withClaim("username", username)
                .withClaim("role", role)
                .withClaim("avatarUrl", avatarUrl)
                .withClaim("bio", bio)
                .withExpiresAt(new Date(System.currentTimeMillis() + getExpirationMillis()))
                .sign(Algorithm.HMAC256(SECRET_KEY));

        // 使旧令牌失效，并将新令牌放入当前有效的令牌中
        String oldToken = currentTokenMap.put(key, newToken);
        // 如果旧令牌存在，将其加入黑名单
        if (oldToken != null) {
            blacklistTokenMap.put(oldToken, System.currentTimeMillis() + getExpirationMillis());
        }

        return newToken;
    }

    /**
     * 验证令牌有效性
     * @param token JWT令牌
     * @return 验证结果码：0=成功，1001=令牌为空，1002=令牌无效，1003=令牌已失效
     */
    public int validateToken(String token) {
        // 检查令牌是否为空
        if (token == null || token.trim().isEmpty()) {
            return 1001; // 令牌为空
        }

        try {
            // 基础验证，验证令牌是否被篡改或无效
            Algorithm algorithm = Algorithm.HMAC256(SECRET_KEY);
            JWT.require(algorithm).build().verify(token);

            // 黑名单校验，检查令牌是否在黑名单中
            if (isTokenBlacklisted(token)) {
                return 1003; // 令牌已失效
            }

            return 0; // 验证通过
        } catch (JWTVerificationException e) {
            return 1002; // 令牌无效
        }
    }

    /**
     * 使令牌失效（退出登录）
     * @param token JWT令牌
     */
    public void invalidateToken(String token) {
        if (token != null && !token.trim().isEmpty()) {
            // 将令牌加入黑名单
            blacklistTokenMap.put(token, System.currentTimeMillis() + getExpirationMillis());

            // 从当前有效令牌中移除
            try {
                DecodedJWT decodedJWT = JWT.decode(token);
                String role = decodedJWT.getClaim("role").asString();
                Long userId = decodedJWT.getClaim("userId").asLong();
                String key = generateKey(role, userId);
                currentTokenMap.remove(key);
            } catch (Exception e) {
                // 忽略解析错误
            }
        }
    }

    /**
     * 从令牌获取用户ID
     * @param token JWT令牌
     * @return 用户ID，解析失败返回null
     */
    public Long getUserIdFromToken(String token) {
        try {
            return JWT.decode(token).getClaim("userId").asLong();
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 从令牌获取用户账号
     * @param token JWT令牌
     * @return 用户账号，解析失败返回null
     */
    public String getUserAccountFromToken(String token) {
        try {
            return JWT.decode(token).getClaim("userAccount").asString();
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 从令牌获取用户名
     * @param token JWT令牌
     * @return 用户名，解析失败返回null
     */
    public String getUsernameFromToken(String token) {
        try {
            return JWT.decode(token).getClaim("username").asString();
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 从令牌获取角色类型
     * @param token JWT令牌
     * @return 角色类型，解析失败返回null
     */
    public String getRoleFromToken(String token) {
        try {
            return JWT.decode(token).getClaim("role").asString();
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 检查令牌是否在黑名单
     * @param token JWT令牌
     * @return true=在黑名单中，false=不在黑名单中
     */
    private boolean isTokenBlacklisted(String token) {
        Long expireTime = blacklistTokenMap.get(token);
        return expireTime != null && expireTime > System.currentTimeMillis();
    }

    /**
     * 清理过期数据
     */
    private static void cleanExpiredData() {
        // 清理过期黑名单令牌
        blacklistTokenMap.entrySet().removeIf(entry -> entry.getValue() <= System.currentTimeMillis());

        // 清理过期用户令牌
        currentTokenMap.entrySet().removeIf(entry -> {
            try {
                Date expiresAt = JWT.decode(entry.getValue()).getExpiresAt();
                return expiresAt != null && new Date().after(expiresAt);
            } catch (Exception e) {
                return true; // 解析失败的令牌也清理掉
            }
        });
    }
}