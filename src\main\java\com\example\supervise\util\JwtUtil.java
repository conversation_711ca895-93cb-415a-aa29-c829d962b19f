package com.example.supervise.util;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.interfaces.DecodedJWT;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@Component
public class JwtUtil {


    // 动态生成密钥，每次启动应用时都会生成新的密钥
    private static final String SECRET_KEY = generateDynamicSecret();
    // 令牌有效期，单位毫秒，设置为6小时
    private static final long EXPIRATION_MILLIS = 1000 * 60 * 60 * 6; // 6小时有效期

    // 身份类型常量
    public static final String IDENTITY_CUSTOMER = "customer";
    public static final String IDENTITY_EMPLOYEE = "employee";
    public static final String IDENTITY_ADMIN = "admin";



    // 存储当前有效的令牌，键为身份+用户ID，值为令牌
    private static final ConcurrentHashMap<String, String> currentTokenMap = new ConcurrentHashMap<>();
    // 存储黑名单中的令牌，键为令牌，值为过期时间戳
    private static final ConcurrentHashMap<String, Long> blacklistTokenMap = new ConcurrentHashMap<>();
    // 存储冻结用户的信息，键为身份+用户ID，值为冻结时间戳
    private static final ConcurrentHashMap<String, Long> frozenUserMap = new ConcurrentHashMap<>();

    // 定时清理任务
    private static final ScheduledExecutorService cleaner = Executors.newSingleThreadScheduledExecutor();

    // 静态初始化，启动定时任务，每小时清理一次过期数据
    static {
        cleaner.scheduleAtFixedRate(JwtUtil::cleanExpiredData, 1, 1, TimeUnit.HOURS);
    }

    // 动态生成密钥
    private static String generateDynamicSecret() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    // 生成复合键：身份+用户ID
    private static String generateKey(String identity, Integer userId) {
        return identity + "_" + userId;
    }

    /**
     * 生成客户令牌
     */
    public String generateCustomerToken(Customer customer) {
        return generateToken(
                //用户id
                customer.getCustomerId(),
                //用户账号
                customer.getUsername(),
                //用户姓名
                customer.getName(),
                //身份类型
                IDENTITY_CUSTOMER,
                //审核状态
                customer.getAuditStatus(),
                //冻结状态
                customer.isFrozenStatus(),
                //头像路径
                customer.getCustomerAvatar(),
                //手机号码
                customer.getPhone()
        );
    }

    /**
     * 生成员工令牌
     */
    public String generateEmployeeToken(Employee employee) {
        return generateToken(
                //员工id
                employee.getEmployeeId(),
                //员工账号
                employee.getUsername(),
                //员工姓名
                employee.getName(),
                //身份类型
                IDENTITY_EMPLOYEE,
                //审核状态
                employee.getAuditStatus(),
                //冻结状态
                employee.isFrozenStatus(),
                //头像路径
                employee.getEmployeeAvatar(),
                //手机号码
                employee.getMobilePhone()
        );
    }

    /**
     * 生成管理员令牌
     */
    public String generateAdminToken(Admin admin) {
        return generateToken(
                //管理员id
                admin.getAdminId(),
                //管理员账号
                admin.getUsername(),
                //管理员姓名
                admin.getName(),
                //身份类型
                IDENTITY_ADMIN,
                "通过", // 管理员默认审核通过
                false, // 管理员默认未冻结
                //管理员头像
                admin.getAdminAvatar(),
                //手机号码
                admin.getMobilePhone()
        );
    }

    /**
     * 通用令牌生成方法
     */
    private String generateToken(
            //用户id
            Integer userId,
            //用户账号
            String username,
            //用户姓名
            String name,
            //身份类型
            String identity,
            //审核状态
            String auditStatus,
            //冻结状态
            boolean frozenStatus,
            //头像路径
            String avatar,
            //手机号码
            String phone
    ) {
        //生成键值对，=> 身份+用户ID
        String key = generateKey(identity, userId);

        //生成令牌，并设置过期时间，单位毫秒，设置为6小时
        String newToken = JWT.create()
                .withClaim("userId", userId)
                .withClaim("username", username)
                .withClaim("name", name)
                .withClaim("identity", identity)
                .withClaim("auditStatus", auditStatus)
                .withClaim("frozenStatus", frozenStatus)
                .withClaim("avatar", avatar)
                .withClaim("phone", phone)
                .withExpiresAt(new Date(System.currentTimeMillis() + EXPIRATION_MILLIS))
                .sign(Algorithm.HMAC256(SECRET_KEY));

        // 使旧令牌失效，并将新令牌放入当前有效的令牌中
        String oldToken = currentTokenMap.put(key, newToken);
        // 如果旧令牌存在，将其加入黑名单
        if (oldToken != null) {
            blacklistTokenMap.put(oldToken, System.currentTimeMillis() + EXPIRATION_MILLIS);
        }

        return newToken;
    }

    /**
     * 验证令牌有效性
     */
    public int validateToken(String token) {
        // 检查令牌是否为空
        if (token == null) {
            return 1001;
        }

        try {
            // 基础验证，验证令牌是否被篡改或无效
            Algorithm algorithm = Algorithm.HMAC256(SECRET_KEY);
            JWT.require(algorithm).build().verify(token);

            // 黑名单校验，检查令牌是否在黑名单中
            if (isTokenBlacklisted(token)) {
                return 1003;
            }

            // 解析令牌
            DecodedJWT decodedJWT = JWT.decode(token);
            // 提取身份和用户ID
            String identity = decodedJWT.getClaim("identity").asString();
            Integer userId = decodedJWT.getClaim("userId").asInt();
            // 提取冻结状态和审核状态
            String auditStatus = decodedJWT.getClaim("auditStatus").asString();
            boolean frozenStatus = decodedJWT.getClaim("frozenStatus").asBoolean();

            // 审核状态校验
            if (!"通过".equals(auditStatus)) {
                return 1004;
            }

            // 冻结状态校验
            if (frozenStatus || isUserFrozen(identity, userId)) {
                return 1005;
            }

            return 0; // 验证通过
        } catch (JWTVerificationException e) {
            return 1002; // 令牌无效
        }
    }

    /**
     * 冻结用户账号
     */
    public void freezeUser(String identity, Integer userId, Long freezeDurationMillis) {
       // 生成键值对，=> 身份+用户ID
        String key = generateKey(identity, userId);

        // 设置冻结状态，0表示永久冻结，其他值表示指定时间冻结
        frozenUserMap.put(
                key,
                freezeDurationMillis == 0 ? 0L : System.currentTimeMillis() + freezeDurationMillis
        );

        // 获取当前有效的令牌，并将其加入黑名单
        String currentToken = currentTokenMap.get(key);
        // 如果当前令牌存在，将其加入黑名单
        if (currentToken != null) {
            blacklistTokenMap.put(currentToken, System.currentTimeMillis() + EXPIRATION_MILLIS);
            currentTokenMap.remove(key);
        }
    }

    /**
     * 解封用户账号
     */
    public void unfreezeUser(String identity, Integer userId) {
        String key = generateKey(identity, userId);
        frozenUserMap.remove(key); // 移除冻结记录
    }

    /**
     * 从令牌获取用户ID
     */
    public Integer getUserIdFromToken(String token) {
        try {
            // 获取令牌中的用户ID，并返回
            return JWT.decode(token).getClaim("userId").asInt();
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 从令牌获取身份类型
     */
    public String getIdentityFromToken(String token) {
        try {
            // 获取令牌中的身份类型，并返回
            return JWT.decode(token).getClaim("identity").asString();
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 检查令牌是否在黑名单
     */
    private boolean isTokenBlacklisted(String token) {
        // 检查令牌是否在黑名单中
        Long expireTime = blacklistTokenMap.get(token);
        // 如果令牌存在且未过期，返回true，否则返回false
        return expireTime != null && expireTime > System.currentTimeMillis();
    }

    /**
     * 检查用户是否被冻结
     */
    private boolean isUserFrozen(String identity, Integer userId) {
        // 检查用户是否被冻结
        String key = generateKey(identity, userId);
        Long freezeExpire = frozenUserMap.get(key);
        // 如果用户未冻结或冻结时间未过期，返回false，否则返回true
        return freezeExpire != null && (freezeExpire == 0 || freezeExpire > System.currentTimeMillis());
    }

    /**
     * 清理过期数据
     */
    private static void cleanExpiredData() {
        // 清理过期黑名单令牌
        blacklistTokenMap.entrySet().removeIf(entry -> entry.getValue() <= System.currentTimeMillis());

        // 清理过期用户令牌
        currentTokenMap.forEach((key, token) -> {
            try {
                Date expiresAt = JWT.decode(token).getExpiresAt();
                if (expiresAt != null && new Date().after(expiresAt)) {
                    currentTokenMap.remove(key);
                }
            } catch (Exception e) {
                currentTokenMap.remove(key);
            }
        });

        // 清理过期冻结状态
        frozenUserMap.entrySet().removeIf(entry ->
                entry.getValue() != 0 && entry.getValue() <= System.currentTimeMillis()
        );
    }
}