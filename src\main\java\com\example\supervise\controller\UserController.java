package com.example.supervise.controller;

import com.example.supervise.dto.UpdateProfileRequest;
import com.example.supervise.service.UserService;
import com.example.supervise.util.Response;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 用户控制器
 * 处理用户信息管理相关的接口请求
 */
@RestController
@RequestMapping("/api/user")
public class UserController {

    private final UserService userService;

    /**
     * 构造函数，注入用户服务
     */
    public UserController(UserService userService) {
        this.userService = userService;
    }

    /**
     * 获取当前用户信息接口
     * @return 用户信息JSON字符串
     */
    @GetMapping("/profile")
    public String getCurrentUserProfile() {
        try {
            // 从Spring Security上下文中获取当前用户ID
            Long userId = getCurrentUserId();
            if (userId == null) {
                return Response.toJSON(401, "用户未登录", null);
            }

            return userService.getUserInfo(userId);

        } catch (Exception e) {
            return Response.toJSON(500, "获取用户信息失败：" + e.getMessage(), null);
        }
    }

    /**
     * 更新用户头像接口
     * @param avatarFile 头像文件
     * @return 更新结果JSON字符串
     */
    @PostMapping("/avatar")
    public String updateAvatar(@RequestParam("avatar") MultipartFile avatarFile) {
        try {
            // 从Spring Security上下文中获取当前用户ID
            Long userId = getCurrentUserId();
            if (userId == null) {
                return Response.toJSON(401, "用户未登录", null);
            }

            return userService.updateAvatar(userId, avatarFile);

        } catch (Exception e) {
            return Response.toJSON(500, "头像更新失败：" + e.getMessage(), null);
        }
    }

    /**
     * 更新用户基本信息接口（用户名和个人简介）
     * @param updateRequest 更新请求信息
     * @return 更新结果JSON字符串
     */
    @PutMapping("/profile")
    public String updateProfile(@RequestBody UpdateProfileRequest updateRequest) {
        try {
            // 从Spring Security上下文中获取当前用户ID
            Long userId = getCurrentUserId();
            if (userId == null) {
                return Response.toJSON(401, "用户未登录", null);
            }

            return userService.updateProfile(userId, updateRequest);

        } catch (Exception e) {
            return Response.toJSON(500, "个人信息更新失败：" + e.getMessage(), null);
        }
    }

    /**
     * 从Spring Security上下文中获取当前用户ID
     * @return 用户ID，如果获取失败返回null
     */
    private Long getCurrentUserId() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.getDetails() instanceof Long) {
                return (Long) authentication.getDetails();
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }
}
