package com.example.supervise.dto;

/**
 * 新增惩罚记录请求DTO
 * 用于接收前端传递的惩罚记录信息
 */
public class AddPunishmentRecordRequest {

    /**
     * 来源类型（1=事项，2=计划，3=日记，4=手动）
     */
    private Integer sourceType;

    /**
     * 来源ID（事项ID/计划ID/日记ID，手动时可为null）
     */
    private Long sourceId;

    /**
     * 惩罚类型ID
     */
    private Long categoryId;

    /**
     * 数量变化类型（1=增加，2=减少，3=清零）
     */
    private Integer changeType;

    /**
     * 变化数量（正数增加，负数减少）
     */
    private Integer changeCount;

    /**
     * 操作原因
     */
    private String reason;

    /**
     * 无参构造函数
     */
    public AddPunishmentRecordRequest() {
    }

    /**
     * 全参构造函数
     */
    public AddPunishmentRecordRequest(Integer sourceType, Long sourceId, Long categoryId,
                                     Integer changeType, Integer changeCount, String reason) {
        this.sourceType = sourceType;
        this.sourceId = sourceId;
        this.categoryId = categoryId;
        this.changeType = changeType;
        this.changeCount = changeCount;
        this.reason = reason;
    }

    /**
     * 获取来源类型
     */
    public Integer getSourceType() {
        return sourceType;
    }

    /**
     * 设置来源类型
     */
    public void setSourceType(Integer sourceType) {
        this.sourceType = sourceType;
    }

    /**
     * 获取来源ID
     */
    public Long getSourceId() {
        return sourceId;
    }

    /**
     * 设置来源ID
     */
    public void setSourceId(Long sourceId) {
        this.sourceId = sourceId;
    }

    /**
     * 获取惩罚类型ID
     */
    public Long getCategoryId() {
        return categoryId;
    }

    /**
     * 设置惩罚类型ID
     */
    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    /**
     * 获取数量变化类型
     */
    public Integer getChangeType() {
        return changeType;
    }

    /**
     * 设置数量变化类型
     */
    public void setChangeType(Integer changeType) {
        this.changeType = changeType;
    }

    /**
     * 获取变化数量
     */
    public Integer getChangeCount() {
        return changeCount;
    }

    /**
     * 设置变化数量
     */
    public void setChangeCount(Integer changeCount) {
        this.changeCount = changeCount;
    }

    /**
     * 获取操作原因
     */
    public String getReason() {
        return reason;
    }

    /**
     * 设置操作原因
     */
    public void setReason(String reason) {
        this.reason = reason;
    }

    /**
     * 验证来源类型是否有效
     */
    public boolean isValidSourceType() {
        return sourceType != null && sourceType >= 1 && sourceType <= 4;
    }

    /**
     * 验证变化类型是否有效
     */
    public boolean isValidChangeType() {
        return changeType != null && changeType >= 1 && changeType <= 3;
    }

    /**
     * 验证变化数量是否有效
     */
    public boolean isValidChangeCount() {
        if (changeCount == null) {
            return false;
        }
        // 清零操作时，变化数量应该为0
        if (changeType != null && changeType == 3) {
            return changeCount == 0;
        }
        // 增加操作时，变化数量应该为正数
        if (changeType != null && changeType == 1) {
            return changeCount > 0;
        }
        // 减少操作时，变化数量应该为负数
        if (changeType != null && changeType == 2) {
            return changeCount < 0;
        }
        return true;
    }

    /**
     * 验证操作原因是否有效
     */
    public boolean isValidReason() {
        return reason == null || reason.length() <= 200;
    }

    /**
     * 验证所有字段是否有效
     */
    public boolean isValid() {
        return isValidSourceType() && isValidChangeType() && 
               isValidChangeCount() && isValidReason() && categoryId != null;
    }

    /**
     * toString方法
     */
    @Override
    public String toString() {
        return "AddPunishmentRecordRequest{" +
                "sourceType=" + sourceType +
                ", sourceId=" + sourceId +
                ", categoryId=" + categoryId +
                ", changeType=" + changeType +
                ", changeCount=" + changeCount +
                ", reason='" + reason + '\'' +
                '}';
    }
}
