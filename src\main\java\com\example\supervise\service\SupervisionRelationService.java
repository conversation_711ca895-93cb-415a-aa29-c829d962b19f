package com.example.supervise.service;

import com.example.supervise.entity.SupervisionRelation;
import com.example.supervise.mapper.SupervisionRelationMapper;
import com.example.supervise.util.Response;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 监督关系服务类
 * 处理监督关系相关的业务逻辑
 */
@Service
public class SupervisionRelationService {

    private final SupervisionRelationMapper supervisionRelationMapper;

    /**
     * 构造函数，注入依赖
     */
    public SupervisionRelationService(SupervisionRelationMapper supervisionRelationMapper) {
        this.supervisionRelationMapper = supervisionRelationMapper;
    }

    /**
     * 根据被监督人ID获取监督关系
     * @param superviseeId 被监督人ID
     * @return 监督关系JSON字符串
     */
    public String getSupervisionRelationBySuperviseeId(Long superviseeId) {
        try {
            SupervisionRelation relation = supervisionRelationMapper.findBySuperviseeId(superviseeId);
            if (relation == null) {
                return Response.toJSON(404, "未找到相关的监督关系", null);
            }
            return Response.toJSON(200, "获取监督关系成功", relation);
        } catch (Exception e) {
            return Response.toJSON(500, "获取监督关系失败：" + e.getMessage(), null);
        }
    }

    /**
     * 根据监督人ID获取所有监督关系
     * @param supervisorId 监督人ID
     * @return 监督关系列表JSON字符串
     */
    public String getSupervisionRelationsBySupervisorId(Long supervisorId) {
        try {
            List<SupervisionRelation> relations = supervisionRelationMapper.findBySupervisorId(supervisorId);
            return Response.toJSON(200, "获取监督关系列表成功", relations);
        } catch (Exception e) {
            return Response.toJSON(500, "获取监督关系列表失败：" + e.getMessage(), null);
        }
    }

    /**
     * 检查监督关系是否存在
     * @param supervisorId 监督人ID
     * @param superviseeId 被监督人ID
     * @return 是否存在监督关系
     */
    public boolean isSupervisionRelationExists(Long supervisorId, Long superviseeId) {
        try {
            int count = supervisionRelationMapper.countBySupervisorIdAndSuperviseeId(supervisorId, superviseeId);
            return count > 0;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 根据被监督人ID获取监督人ID
     * @param superviseeId 被监督人ID
     * @return 监督人ID，如果不存在返回null
     */
    public Long getSupervisorIdBySuperviseeId(Long superviseeId) {
        try {
            SupervisionRelation relation = supervisionRelationMapper.findBySuperviseeId(superviseeId);
            return relation != null ? relation.getSupervisorId() : null;
        } catch (Exception e) {
            return null;
        }
    }
}
