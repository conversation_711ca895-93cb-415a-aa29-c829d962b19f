package com.example.supervise.entity;

import java.time.LocalDateTime;

/**
 * 监督关系实体类
 * 对应数据库表：supervision_relation
 */
public class SupervisionRelation {

    /**
     * 关系主键ID
     */
    private Long relationId;

    /**
     * 监督人ID（外键）
     */
    private Long supervisorId;

    /**
     * 被监督人ID（外键）
     */
    private Long superviseeId;

    /**
     * 默认惩罚类型ID（外键）
     */
    private Long defaultPunishmentCategory;

    /**
     * 默认惩罚数量
     */
    private Integer defaultPunishmentCount;

    /**
     * 关联创建时间
     */
    private LocalDateTime createTime;

    /**
     * 无参构造函数
     */
    public SupervisionRelation() {
    }

    /**
     * 全参构造函数
     */
    public SupervisionRelation(Long relationId, Long supervisorId, Long superviseeId, 
                              Long defaultPunishmentCategory, Integer defaultPunishmentCount, 
                              LocalDateTime createTime) {
        this.relationId = relationId;
        this.supervisorId = supervisorId;
        this.superviseeId = superviseeId;
        this.defaultPunishmentCategory = defaultPunishmentCategory;
        this.defaultPunishmentCount = defaultPunishmentCount;
        this.createTime = createTime;
    }

    /**
     * 获取关系主键ID
     */
    public Long getRelationId() {
        return relationId;
    }

    /**
     * 设置关系主键ID
     */
    public void setRelationId(Long relationId) {
        this.relationId = relationId;
    }

    /**
     * 获取监督人ID
     */
    public Long getSupervisorId() {
        return supervisorId;
    }

    /**
     * 设置监督人ID
     */
    public void setSupervisorId(Long supervisorId) {
        this.supervisorId = supervisorId;
    }

    /**
     * 获取被监督人ID
     */
    public Long getSuperviseeId() {
        return superviseeId;
    }

    /**
     * 设置被监督人ID
     */
    public void setSuperviseeId(Long superviseeId) {
        this.superviseeId = superviseeId;
    }

    /**
     * 获取默认惩罚类型ID
     */
    public Long getDefaultPunishmentCategory() {
        return defaultPunishmentCategory;
    }

    /**
     * 设置默认惩罚类型ID
     */
    public void setDefaultPunishmentCategory(Long defaultPunishmentCategory) {
        this.defaultPunishmentCategory = defaultPunishmentCategory;
    }

    /**
     * 获取默认惩罚数量
     */
    public Integer getDefaultPunishmentCount() {
        return defaultPunishmentCount;
    }

    /**
     * 设置默认惩罚数量
     */
    public void setDefaultPunishmentCount(Integer defaultPunishmentCount) {
        this.defaultPunishmentCount = defaultPunishmentCount;
    }

    /**
     * 获取关联创建时间
     */
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    /**
     * 设置关联创建时间
     */
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    /**
     * toString方法
     */
    @Override
    public String toString() {
        return "SupervisionRelation{" +
                "relationId=" + relationId +
                ", supervisorId=" + supervisorId +
                ", superviseeId=" + superviseeId +
                ", defaultPunishmentCategory=" + defaultPunishmentCategory +
                ", defaultPunishmentCount=" + defaultPunishmentCount +
                ", createTime=" + createTime +
                '}';
    }

    /**
     * equals方法
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        SupervisionRelation that = (SupervisionRelation) obj;
        return relationId != null && relationId.equals(that.relationId);
    }

    /**
     * hashCode方法
     */
    @Override
    public int hashCode() {
        return relationId != null ? relationId.hashCode() : 0;
    }
}
