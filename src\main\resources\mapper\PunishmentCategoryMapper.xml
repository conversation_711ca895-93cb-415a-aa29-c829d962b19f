<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.example.supervise.mapper.PunishmentCategoryMapper">

    <!-- 惩罚类型结果映射 -->
    <resultMap id="PunishmentCategoryResultMap" type="com.example.supervise.entity.PunishmentCategory">
        <id property="categoryId" column="category_id"/>
        <result property="supervisorId" column="supervisor_id"/>
        <result property="categoryName" column="category_name"/>
        <result property="categoryDesc" column="category_desc"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <!-- 根据监督人ID查询所有惩罚类型 -->
    <select id="findBySupervisorId" resultMap="PunishmentCategoryResultMap">
        SELECT 
            category_id,
            supervisor_id,
            category_name,
            category_desc,
            create_time
        FROM punishment_categories 
        WHERE supervisor_id = #{supervisorId}
        ORDER BY create_time DESC
    </select>

    <!-- 根据类型ID查询惩罚类型 -->
    <select id="findByCategoryId" resultMap="PunishmentCategoryResultMap">
        SELECT 
            category_id,
            supervisor_id,
            category_name,
            category_desc,
            create_time
        FROM punishment_categories 
        WHERE category_id = #{categoryId}
    </select>

    <!-- 根据监督人ID和类型名称查询惩罚类型（用于检查重名） -->
    <select id="findBySupervisorIdAndCategoryName" resultMap="PunishmentCategoryResultMap">
        SELECT 
            category_id,
            supervisor_id,
            category_name,
            category_desc,
            create_time
        FROM punishment_categories 
        WHERE supervisor_id = #{supervisorId} 
        AND category_name = #{categoryName}
    </select>

    <!-- 根据监督人ID和类型名称查询惩罚类型（排除指定ID，用于编辑时检查重名） -->
    <select id="findBySupervisorIdAndCategoryNameExcludeId" resultMap="PunishmentCategoryResultMap">
        SELECT 
            category_id,
            supervisor_id,
            category_name,
            category_desc,
            create_time
        FROM punishment_categories 
        WHERE supervisor_id = #{supervisorId} 
        AND category_name = #{categoryName}
        AND category_id != #{excludeCategoryId}
    </select>

    <!-- 新增惩罚类型 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="categoryId">
        INSERT INTO punishment_categories (
            supervisor_id,
            category_name,
            category_desc,
            create_time
        ) VALUES (
            #{supervisorId},
            #{categoryName},
            #{categoryDesc},
            #{createTime}
        )
    </insert>

    <!-- 更新惩罚类型 -->
    <update id="update">
        UPDATE punishment_categories 
        SET category_name = #{categoryName},
            category_desc = #{categoryDesc}
        WHERE category_id = #{categoryId}
    </update>

    <!-- 删除惩罚类型 -->
    <delete id="delete">
        DELETE FROM punishment_categories 
        WHERE category_id = #{categoryId} 
        AND supervisor_id = #{supervisorId}
    </delete>

    <!-- 检查惩罚类型是否被使用（在事项惩罚关联表中） -->
    <select id="countUsageInMatterPunishments" resultType="int">
        SELECT COUNT(*) 
        FROM matter_punishments 
        WHERE category_id = #{categoryId}
    </select>

    <!-- 检查惩罚类型是否被使用（在计划惩罚关联表中） -->
    <select id="countUsageInPlanPunishments" resultType="int">
        SELECT COUNT(*) 
        FROM plan_punishments 
        WHERE category_id = #{categoryId}
    </select>

    <!-- 检查惩罚类型是否被使用（在惩罚记录表中） -->
    <select id="countUsageInPunishmentRecords" resultType="int">
        SELECT COUNT(*) 
        FROM punishment_records 
        WHERE category_id = #{categoryId}
    </select>

    <!-- 检查惩罚类型是否被设置为默认惩罚类型 -->
    <select id="countUsageAsDefaultPunishment" resultType="int">
        SELECT COUNT(*) 
        FROM supervision_relation 
        WHERE default_punishment_category = #{categoryId}
    </select>

</mapper>
